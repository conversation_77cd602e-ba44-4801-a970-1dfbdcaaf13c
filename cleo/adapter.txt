%REM
	Library Adapter
	Created Nov 7, 2018 by Code Signer/sehnp
	Description: Comments for Library
%END REM
Option Public
Option Declare




Use "com.seh.cleo.ApplicationDatabase"
%REM
	Class Adapter
	Description: Comments for Class
%END REM
Public Class Adapter
	
'	Private appDb As ApplicationDatabase
	Private stopWatch As Stopwatch
'	Private docConfig As NotesDocument
	Private isAdapterEnabled As Boolean
	Private logTiming As Boolean
	Private consoleOutput As Boolean
	Private logPayload As Boolean
	Private endPoint As String
	
'	Private isTimingEnabled As Boolean
'	Private isConsoleEnabled As Boolean
	
	%REM
		Sub New
		Description: Comments for Sub
	%END REM
'	Public Sub New(app As ApplicationDatabase, adapterConfig As AdapterConfig)
'		Set Me.appDb = app
'		Set Me.stopWatch = New Stopwatch()
'		Set Me.docConfig = app.getDatabaseDBConfig(Nothing)
'		
'		Me.logTiming = Me.docConfig.Getitemvalue("Adapter_Enabled_Time")(0) = "1"
'		Me.consoleOutput = Me.docConfig.Getitemvalue("Adapter_Enabled_Console")(0) = "1"
'		Me.logPayload = Me.docConfig.Getitemvalue("Adapter_Enabled_Log_Payload")(0) = "1"
'	End Sub

	Public Sub New(adapterConfig As AdapterConfig)
'		Set Me.appDb = app
		Set Me.stopWatch = New Stopwatch()
'		Set Me.docConfig = app.getDatabaseDBConfig(Nothing)
		
		Me.isAdapterEnabled = adapterConfig.isAdapterEnabled
		Me.logTiming = adapterConfig.logTiming
		Me.consoleOutput = adapterConfig.consoleOutput
		Me.logPayload = adapterConfig.logPayload
		Me.endPoint = adapterConfig.endPoint
	End Sub
	
	%REM
		Function transferCallDirect
		Description: Comments for Function
	%END REM
	Public Function transferCallDirect( doc As NotesDocument, apiUrl As String )
		On Error GoTo ErrorHandler
		
		Dim json As String
		Dim httpObject As Variant
		Dim req As String
		Dim serviceUrlRoot As String
		Dim serviceApi As String
		Dim logPayload As Boolean
		Dim logMessage As String
		Dim responseBody As Variant
		Dim responseStatus As Variant
		Dim errorMessage As String
		Dim callNo As String
		
		If Not Me.isAdapterEnabled Then	
			'MsgBox {Adapter transferCallDirect is NOT enabled}
			Exit Function
		End If
		
		If apiUrl = "" Then
			MsgBox {Adapter transferCallDirect is enabled but no API URL}
			Call logErrorEx( {Adapter transferCallDirect is enabled but no API URL}, {SEVERITY_HIGH}, Nothing )
			Exit Function
		End If
		
'		If Me.consoleOutput Then MsgBox "Adapter.transferCallDirect()...connecting to: " & Me.endPoint
		
		callNo = doc.Getitemvalue("CallNo")(0) & ""
		If me.consoleOutput Then MsgBox {Adapter transferCallDirect callno: } & callNo
		
		If me.logTiming Then Me.stopWatch.Start("buildCallPayload")
		json = me.buildCallPayload(doc)
		If me.logTiming Then Me.stopWatch.Stop("buildCallPayload")
		'//  MsgBox json
		'//  Exit Function
		
		apiUrl = apiUrl + "/api/calls/postcall"
		If Me.consoleOutput Then MsgBox "Adapter.transferCallDirect()...apiUrl: " & apiUrl
		
		If me.logTiming Then Me.stopWatch.Start("set up http conn")
		Set httpObject = CreateObject("MSXML2.ServerXMLHTTP.6.0")
		Call httpObject.setOption( 2, 13056 )	'//	ignore cert errors...!
		Call httpObject.open("POST", apiUrl, False)
		Call httpObject.setRequestHeader("Content-Type", "application/json")
		httpObject.SetRequestHeader "Content-Length", Len(json)
		If me.logTiming Then Me.stopWatch.Stop("set up http conn")
		
		If me.logTiming Then Me.stopWatch.Start("http conn send")
		
'		json = |{
'  "PageNumber": 1,
'  "RecordsPerPage": 10,
'  "QueryName": "111calls",
'  "Criteria": {}
'} |
		
		Call httpObject.send(json & "")
		If me.logTiming Then Me.stopWatch.Stop("http conn send")
		
		If me.logTiming Then Me.stopWatch.Start("http response stuff")
		responseStatus = Val(httpObject.status)
		responseBody = httpObject.responseText
		
		If responseStatus < 200 Or responseStatus >= 300 Then
			errorMessage = "CallNo: " & doc.Getitemvalue("CallNo")(0) & ", ERROR: " & responseStatus & "  " & responseBody
			If me.consoleOutput Then MsgBox {Adapter transferCallDirect } & errorMessage
			Call logErrorEx( errorMessage, {SEVERITY_HIGH}, doc )
			
		Else
			If me.consoleOutput Then MsgBox {Adapter transferCallDirect callno: } & callNo & "...SUCCESS"
		End If
		If me.logTiming Then Me.stopWatch.Stop("http response stuff")
		
		Dim dblTotal As Double
		Const THRESHOLD_TIME_MILLI_SECS = 500
		
		dblTotal = Me.stopWatch.GetTime("Total run time")
		If dblTotal  > THRESHOLD_TIME_MILLI_SECS Then
			Call logEvent({transferCallDirect@ timing for call #: [} & callNo & {], threshold: } & THRESHOLD_TIME_MILLI_SECS & {ms } & Chr(13) & Me.stopWatch.GetAllWatchValues(),{SEVERITY_LOW}, doc )
			MsgBox {Adapter transferCallDirect callno: } & callNo & "...DEBUG...logged timing, this may affect performance.  See CallD config."  
		End If
		
		If Me.logPayload Then
			Call logEvent({Adapter Payload #: [} & callNo & "] " & json & "RESP>>>" & responseBody,{SEVERITY_LOW}, doc )
			MsgBox {Adapter transferCallDirect callno: } & callNo & "...DEBUG...logged payload, this may affect performance.  See CallD config."
		End If
		
SimpleExit:
		Exit Function
ErrorHandler:
		MsgBox "Adapter.transferCall() Line: " & Erl() & ", Error: " & Error
		Call logErrorEx( {payload>>>} & json, {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	%REM
		Function transferCall
		Description: Comments for Function
	%END REM
	Public Function transferCall(id As string)
		
		Dim json As String
		Dim httpObject As Variant
		Dim req As String
		Dim serviceUrlRoot As String
		Dim serviceApi As String
		Dim logPayload As Boolean
		Dim logMessage As String
		Dim responseBody As Variant
		
		Dim sb As New StringBuffer(16)
		
		Const DEBUG_THIS = True
		
		MsgBox "Adapter.transferCall() Start..."
		
	
		'ADAPTER_JSON
		Dim path As String

		'serviceApi = "http://" + Me.hostName + "/" + me.xcleoPath + "/xpbeaninterface.xsp?action=ADAPTER_JSON&processformat=json&sid=" & id



		Set httpObject = CreateObject("MSXML2.ServerXMLHTTP.6.0")
		Call httpObject.setOption( 2, 13056 )	'//	ignore cert errors...!
		Call httpObject.open("GET", serviceApi, False)
		'Call httpObject.setRequestHeader("Content-Type", "application/json")
		'httpObject.SetRequestHeader "Content-Length", Len(json)
		
		Call httpObject.send("")
		
		responseBody = httpObject.responseText
		
		MsgBox "Adapter.transferCall() ...Finish."
		
	End Function
	
	
	
	Public Function buildCallPayload( doc As NotesDocument) As String
		On Error GoTo ErrorHandler
	
		Dim sb As New StringBuffer(16)
		Dim item As NotesItem
		Dim fieldName As String
		Dim je As New JsonEncoder()
		Dim counter As Integer
		Dim dateParserUtil As New DateParserUtil(Nothing )
		Dim transferValue As String
		Dim ndt As NotesDateTime
		Dim flds As Variant

		Call sb.Append(|{|)
		
		counter = 0
		
		flds = Me.getFieldsToTransferList()
		ForAll fld In flds
			
			If fld & "" <> "" Then
				Set item = doc.Getfirstitem(fld & "")

'				fieldName = item.Name
				counter = counter + 1
				
				If counter > 1 Then
					Call sb.Append(|,|)	
				End If
				
				Call sb.Append(|"|)
				Call sb.Append(fld)	
				Call sb.Append(|"|)	
				
				Call sb.Append(|:|)	
				Call sb.Append(|"|)	

				If Not (item Is Nothing) Then
					If item.Type = DATETIMES Then
						Set ndt = New NotesDateTime(item.Values(0))
						transferValue = dateParserUtil.ndtToIsoWithOffset(ndt)
					Else
						transferValue = item.Text
					End If
					
					'MsgBox counter & " - " & fieldName & " - " & transferValue
					Call sb.Append(je.JsonEncode(transferValue))
				End If
				
				
				Call sb.Append(|"|)	
				
			End If
			
			'Msgbox counter & " - " & fld & ""
			
			
		End ForAll		
		
		Call sb.Append(|}|)
		
		buildCallPayload = sb.toString()
	
SimpleExit:
		Exit Function
		
ErrorHandler:
		MsgBox "Adapter.transferCall() Line: " & Erl() & ", Error: " & Error
		Call logErrorEx( {}, {SEVERITY_HIGH}, Nothing )
		Resume SimpleExit
	End Function
	
	%REM
		Function getFieldsToTransferList
		Description: Comments for Function
	%END REM
	Private Function getFieldsToTransferList() As Variant
		
		Dim flds As Variant
				'flds = ||
				'flds = flds & |auditNotPossible;auditNotPossibleReason;AFT_UNABLE_REASON;AppointmentBreachReason;AppointmentBreachReasonComment;AppointmentBreachReasonTime;AppointmentBreachReasonUser;BreachActualTime;BreachActualTimeNDT;BreachActualTimeNDT_CDisp;BreachActualTimeNDT_Speak;BreachClassChangeReason;BreachPreActualTime;BreachPriority;BreachPriorityQueueRestrict;Call1stContact;Call1stContactPathways;CallAddress1;CallAddress2;CallAddress3;CallAddress4;CallAdmissionAvoidance;CallArrivedTime;CallAuditedDateGP;CallCallback;CallClassification;CallCName;CallComments;CallCompleted;CallCreatedBy;CallCreatedDate;CallCRel;CallCTS;CallDetails;CallDNPresentingCondition;CallDNTreatmentCondition;CallDoctorName;CallDoctorNameCN;CallDOB;CallDTSAgentYN;CallEthnicity;CallFirstSavedDateTime;CallGP;CallID;CallInformationalOutcomes;CallInformationalOutcomesComment;CallInformationalOutcomesID;CallInformationalSubOutcomes;CallLast72;CallMF;CallNHSNo;CallNo;CallNTriageName;CallPassedTo999;CallPassedTo999Time;CallPatientInstructions;CallPostCode;CallPractice;CallPracticeIDNo;CallPracticeOCS;CallPTA;CallReceived;CallReceivedTime;CallReceivedFromAdastra;CallRGPPaperworkReceived;CallRGPPatCalledBackTime;CallRGPPatCalledTime;CallRGPReferralSource;CallRGPServiceNA;CallSecondOpenCall;CallSentToAdastra;CallSentToAdastra;CallService;CallServiceOriginal;CallServiceType;CallSEHDetailsSentTimeStamp;CallSPA;CallSPAReferal;CallSPAReferalService;CallSPAReferalUrgency;CallStatus;CallStatusValue;CallSubClassification;CallSymptoms;CallTelNo;CallTelNoAltType_1;CallTelNoAlt_1;CallTelNo_R;CallTelNo_Type;CallTempResYN;CallTest;CallTEDT;CallToDoctor;CallTown;CallTriaged;CallTSDT;CallUrgentAfterTriage;CallUrgentAtFirstContact;CallUrgentOnReception;CallUrgentYN;CallWalkinYN;CallWarmTransferred;CallWarmTransferredTime;CallWithBaseAckTime;Call_Interpreter;CADCaseID;CAGPAuditor;CAGPAuditType;CAGPCancel;CAGPCFR;CAGPComments;CAGPOutcome;CAGPStatus;CAS_TRANSFER_ERROR;ChildProtectionLkUp;ChildProtectionReferral;CHFinalDispositionCode;CHFinalDispositionDescription;CHPathwaysExitType;CHTriageDispositionCode;CHTriageDispositionDescription;ClinicalHub_111ToHubReason;ClinicalHub_Data_CallClassification;ClinicalHub_Data_CallUrgentYN;ClinicalHub_from111CallNo;ClinicalHub_fromCliniHubCallNo;ClinicalHub_OutcomeAvoided;ClinicalHub_OutcomeAvoided_Reason;ClinicalHub_OutcomeOOHTransfer;ClinicalHub_OutcomePriority;ClinicalHub_toHubCallNo;ClinicalHub_toOohCallNo;CliniHighPriority;Courtesy_Array;Courtesy_contact;Courtesy_Comment;Courtesy_Count;Courtesy_TimeNDT;Courtesy_User;DailyIndividualResult;DateAppointmentEnd;DateAppointmentStart;Dispatch_Vehicle;DMP_HasBeen;DocID;DocumentUniqueID;DOSAccessNotes;DOSAddress;DOSCapacity;DOSContactDetails;DOSOpen24Hrs;DOSResults;DOSSearch;DOSServiceID;DOSServiceName;DOSServices;DOSSymptoms;DtArrivedTime;DutyArea;DutyBase;DutyRef;EnhancedOOH;FinalDispositionCode;FinalDispositionDescription;GPID;GPUsername;HubLink_111CallClassification;HubLink_111CallNo;HubLink_111CallUrgentYN;HubLink_111Time;HubLink_111User;HubLink_HubCallClassification;HubLink_HubCallNo;HubLink_HubCallUrgentYN;HubLink_HubTime;HubLink_HubUser;HubLink_OohCallUrgentYN;HubLink_OOHCallClassification;HubLink_OOHCallNo;HubLink_OOHTime;HubLink_OOHUser;ITK_111_Online;ITK_111_Online_To_111;ITK_DX;Linked_Call_ID;Night_Sitting;NonClini_Action_Time;NonClini_Action_User;PalliativeCareLkUp;PathwaysCaseId;PathwaysClassification;PathwaysExitType;PathwaysUrgency;Pathways_ITK_Send;PatientContactCode;PatientContactCodeComment;PatientInstructionLkUp;PatientPostCode;PCT;PDSSCN;PDSSCRGUID;PDSSCRRetrieved;PDSSCRViewed;PDSTracedAndVerified;PEM_DTS_ADDRESS;PEM_EMAIL_ADDRESS;PEM_TRANSFER_METHOD;PEM_TRANSFER_RESULT;PEM_TRANSFER_TIME;PEQ_mobile;PlannedVisit;POSL;ReceivedOrganization;RefDosFeedback;RefDosPatFeedback;RefResult;RefService;ScrAlertAssessmentQuality;ScrAlertChangeDisposition;ScrAlertChangeDispositionCode;ScrAlertComments;ScrAlertEnd;ScrAlertImpact;ScrAlertImpactDuration;ScrAlertStart;ServiceCaseReference;ShareMyCare_Link;ShareMyCare_Message;ShareMyCare_Type;ShareMyCare_Xml;SRC_CLEO_CALL_REF;StartConsultationPerformed;SummaryLocationArea;txtPatientID;TimeAppointmentEnd;TimeAppointmentStart;TomTomOrderID;TriageDispositionCode;TriageDispositionDescription;TxtAppointmentBase;ValidQR10BreachAprov;ValidQR10BreachAprovDateTime;ValidQR10BreachAprovName;ValidQR10BreachEnterDateTime;ValidQR10BreachEnterName;ValidQR10BreachNotes;ValidQR10BreachReason;ValidQR10ExceptionEnterDateTime;ValidQR10ExceptionEnterName;ValidQR10ExceptionNotes;ValidQR10ExceptionReason;ValidQR10ValidationAprov;ValidQR10ValidationAprovDateTime;ValidQR10ValidationAprovName;ValidQR10ValidationEnterDateTime;ValidQR10ValidationEnterName;ValidQR10ValidationNotes;ValidQR10ValidationReason;ValidQR12BreachAprov;ValidQR12BreachAprovDateTime;ValidQR12BreachAprovName;ValidQR12BreachEnterDateTime;ValidQR12BreachEnterName;ValidQR12BreachNotes;ValidQR12BreachReason;ValidQR12ExceptionEnterDateTime;ValidQR12ExceptionEnterName;ValidQR12ExceptionNotes;ValidQR12ExceptionReason;ValidQR12ValidationAprov;ValidQR12ValidationAprovDateTime;ValidQR12ValidationAprovName;ValidQR12ValidationEnterDateTime;ValidQR12ValidationEnterName;ValidQR12ValidationNotes;ValidQR12ValidationReason;ValidQR9BreachAprov;ValidQR9BreachAprovDateTime;ValidQR9BreachAprovName;ValidQR9BreachEnterDateTime;ValidQR9BreachEnterName;ValidQR9BreachNotes;ValidQR9BreachReason;ValidQR9ExceptionEnterDateTime;ValidQR9ExceptionEnterName;ValidQR9ExceptionNotes;ValidQR9ExceptionReason;ValidQR9ValidationAprov;ValidQR9ValidationAprovDateTime;ValidQR9ValidationAprovName;ValidQR9ValidationEnterDateTime;ValidQR9ValidationEnterName;ValidQR9ValidationNotes;ValidQR9ValidationReason;VulnerableAdultReferral;WalkIn;JunkField|
		'		flds = flds & |AppointmentBreachReasonTime;AppointmentBreachReasonUser;BreachActualTimeNDT;BreachActualTimeNDT_CDisp;BreachActualTimeNDT_Speak;BreachClassChangeReason;BreachPriorityQueueRestrict;CallPatientInstructions;CallReceivedFromAdastra;CallSEHDetailsSentTimeStamp;CallSentToAdastra;ChildProtectionLkUp;Courtesy_Count;Courtesy_TimeNDT;DailyIndividualResult;DocumentUniqueID;DOSAccessNotes;DOSAddress;DOSContactDetails;DOSResults;DOSSearch;DOSServices;DOSSymptoms;DutyArea;DutyRef;HubLink_111CallClassification;HubLink_111CallNo;HubLink_111CallUrgentYN;HubLink_111Time;HubLink_111User;HubLink_HubCallClassification;HubLink_HubCallNo;HubLink_HubCallUrgentYN;HubLink_HubTime;HubLink_HubUser;HubLink_OOHCallClassification;HubLink_OOHCallNo;HubLink_OohCallUrgentYN;HubLink_OOHTime;HubLink_OOHUser;ITK_111_Online;ITK_111_Online_To_111;ITK_DX;NonClini_Action_Time;NonClini_Action_User;PalliativeCareLkUp;PatientInstructionLkUp;PEM_DTS_ADDRESS;PEM_EMAIL_ADDRESS;PEM_TRANSFER_METHOD;PEM_TRANSFER_RESULT;PEM_TRANSFER_TIME;POSL;ShareMyCare_Xml;SRC_CLEO_CALL_REF;ValidQR10BreachAprovDateTime;ValidQR10BreachEnterDateTime;ValidQR10ExceptionEnterDateTime;ValidQR10ValidationAprovDateTime;ValidQR10ValidationEnterDateTime;ValidQR12BreachAprovDateTime;ValidQR12BreachEnterDateTime;ValidQR12ExceptionEnterDateTime;ValidQR12ValidationAprovDateTime;ValidQR12ValidationEnterDateTime;ValidQR9BreachAprovDateTime;ValidQR9BreachEnterDateTime;ValidQR9ExceptionEnterDateTime;ValidQR9ValidationAprovDateTime;ValidQR9ValidationEnterDateTime;|
		flds = |auditNotPossible;auditNotPossibleReason;AFT_UNABLE_REASON;AppointmentBreachReason;AppointmentBreachReasonComment;AppointmentBreachReasonTime;AppointmentBreachReasonUser;BreachActualTime;BreachActualTimeNDT;BreachActualTimeNDT_CDisp;BreachActualTimeNDT_Speak;BreachClassChangeReason;BreachPreActualTime;BreachPriority;BreachPriorityQueueRestrict;Call1stContact;Call1stContactPathways;CallAddress1;CallAddress2;CallAddress3;CallAddress4;CallAdmissionAvoidance;CallArrivedTime;CallAuditedDateGP;CallCallback;CallClassification;CallCName;CallComments;CallCompleted;CallCreatedBy;CallCreatedDate;CallCRel;CallCTS;CallDetails;CallDNPresentingCondition;CallDNTreatmentCondition;CallDoctorName;CallDoctorNameCN;CallDOB;CallDTSAgentYN;CallEthnicity;CallFirstSavedDateTime;CallGP;CallID;CallInformationalOutcomes;CallInformationalOutcomesComment;CallInformationalOutcomesID;CallInformationalSubOutcomes;CallLast72;CallMF;CallNHSNo;CallNo;CallNTriageName;CallPassedTo999;CallPassedTo999Time;CallPatientInstructions;CallPostCode;CallPractice;CallPracticeIDNo;CallPracticeOCS;CallPTA;CallReceived;CallReceivedTime;CallReceivedFromAdastra;CallRGPPaperworkReceived;CallRGPPatCalledBackTime;CallRGPPatCalledTime;CallRGPReferralSource;CallRGPServiceNA;CallSecondOpenCall;CallSentToAdastra;CallService;CallServiceOriginal;CallServiceType;CallSEHDetailsSentTimeStamp;CallSPA;CallSPAReferal;CallSPAReferalService;CallSPAReferalUrgency;CallStatus;CallStatusValue;CallSubClassification;CallSymptoms;CallTelNo;CallTelNoAltType_1;CallTelNoAlt_1;CallTelNo_R;CallTelNo_Type;CallTempResYN;CallTest;CallTEDT;CallToDoctor;CallTown;CallTriaged;CallTSDT;CallUrgentAfterTriage;CallUrgentAtFirstContact;CallUrgentOnReception;CallUrgentYN;CallWalkinYN;CallWarmTransferred;CallWarmTransferredTime;CallWithBaseAckTime;Call_Interpreter;CADCaseID;CAGPAuditor;CAGPAuditType;CAGPCancel;CAGPCFR;CAGPComments;CAGPOutcome;CAGPStatus;CAS_TRANSFER_ERROR;ChildProtectionLkUp;ChildProtectionReferral;CHFinalDispositionCode;CHFinalDispositionDescription;CHPathwaysExitType;CHTriageDispositionCode;CHTriageDispositionDescription;ClinicalHub_111ToHubReason;ClinicalHub_Data_CallClassification;ClinicalHub_Data_CallUrgentYN;ClinicalHub_from111CallNo;ClinicalHub_fromCliniHubCallNo;ClinicalHub_OutcomeAvoided;ClinicalHub_OutcomeAvoided_Reason;ClinicalHub_OutcomeOOHTransfer;ClinicalHub_OutcomePriority;ClinicalHub_toHubCallNo;ClinicalHub_toOohCallNo;CliniHighPriority;Courtesy_Array;Courtesy_contact;Courtesy_Comment;Courtesy_Count;Courtesy_TimeNDT;Courtesy_User;DailyIndividualResult;DateAppointmentEnd;DateAppointmentStart;Dispatch_Vehicle;DMP_HasBeen;DocID;DocumentUniqueID;DOSAccessNotes;DOSAddress;DOSCapacity;DOSContactDetails;DOSOpen24Hrs;DOSResults;DOSSearch;DOSServiceID;DOSServiceName;DOSServices;DOSSymptoms;DtArrivedTime;DutyArea;DutyBase;DutyRef;EnhancedOOH;FinalDispositionCode;FinalDispositionDescription;GPID;GPUsername;HubLink_111CallClassification;HubLink_111CallNo;HubLink_111CallUrgentYN;HubLink_111Time;HubLink_111User;HubLink_HubCallClassification;HubLink_HubCallNo;HubLink_HubCallUrgentYN;HubLink_HubTime;HubLink_HubUser;HubLink_OohCallUrgentYN;HubLink_OOHCallClassification;HubLink_OOHCallNo;HubLink_OOHTime;HubLink_OOHUser;ITK_111_Online;ITK_111_Online_To_111;ITK_DX;Linked_Call_ID;Night_Sitting;NonClini_Action_Time;NonClini_Action_User;PalliativeCareLkUp;PathwaysCaseId;PathwaysClassification;PathwaysExitType;PathwaysUrgency;Pathways_ITK_Send;PatientContactCode;PatientContactCodeComment;PatientInstructionLkUp;PatientPostCode;PCT;PDSSCN;PDSSCRGUID;PDSSCRRetrieved;PDSSCRViewed;PDSTracedAndVerified;PEM_DTS_ADDRESS;PEM_EMAIL_ADDRESS;PEM_TRANSFER_METHOD;PEM_TRANSFER_RESULT;PEM_TRANSFER_TIME;PEQ_mobile;PlannedVisit;POSL;ReceivedOrganization;RefDosFeedback;RefDosPatFeedback;RefResult;RefService;ScrAlertAssessmentQuality;ScrAlertChangeDisposition;ScrAlertChangeDispositionCode;ScrAlertComments;ScrAlertEnd;ScrAlertImpact;ScrAlertImpactDuration;ScrAlertStart;ServiceCaseReference;ShareMyCare_Link;ShareMyCare_Message;ShareMyCare_Type;ShareMyCare_Xml;SRC_CLEO_CALL_REF;StartConsultationPerformed;SummaryLocationArea;txtPatientID;TimeAppointmentEnd;TimeAppointmentStart;TomTomOrderID;TriageDispositionCode;TriageDispositionDescription;TxtAppointmentBase;ValidQR10BreachAprov;ValidQR10BreachAprovDateTime;ValidQR10BreachAprovName;ValidQR10BreachEnterDateTime;ValidQR10BreachEnterName;ValidQR10BreachNotes;ValidQR10BreachReason;ValidQR10ExceptionEnterDateTime;ValidQR10ExceptionEnterName;ValidQR10ExceptionNotes;ValidQR10ExceptionReason;ValidQR10ValidationAprov;ValidQR10ValidationAprovDateTime;ValidQR10ValidationAprovName;ValidQR10ValidationEnterDateTime;ValidQR10ValidationEnterName;ValidQR10ValidationNotes;ValidQR10ValidationReason;ValidQR12BreachAprov;ValidQR12BreachAprovDateTime;ValidQR12BreachAprovName;ValidQR12BreachEnterDateTime;ValidQR12BreachEnterName;ValidQR12BreachNotes;ValidQR12BreachReason;ValidQR12ExceptionEnterDateTime;ValidQR12ExceptionEnterName;ValidQR12ExceptionNotes;ValidQR12ExceptionReason;ValidQR12ValidationAprov;ValidQR12ValidationAprovDateTime;ValidQR12ValidationAprovName;ValidQR12ValidationEnterDateTime;ValidQR12ValidationEnterName;ValidQR12ValidationNotes;ValidQR12ValidationReason;ValidQR9BreachAprov;ValidQR9BreachAprovDateTime;ValidQR9BreachAprovName;ValidQR9BreachEnterDateTime;ValidQR9BreachEnterName;ValidQR9BreachNotes;ValidQR9BreachReason;ValidQR9ExceptionEnterDateTime;ValidQR9ExceptionEnterName;ValidQR9ExceptionNotes;ValidQR9ExceptionReason;ValidQR9ValidationAprov;ValidQR9ValidationAprovDateTime;ValidQR9ValidationAprovName;ValidQR9ValidationEnterDateTime;ValidQR9ValidationEnterName;ValidQR9ValidationNotes;ValidQR9ValidationReason;VulnerableAdultReferral;WalkIn|
		getFieldsToTransferList = Split(flds, ";")
		
	End Function
	
	
End Class
%REM
	Class AdapterConfig
	Description: Comments for Class
%END REM
Public Class AdapterConfig
	
	Public isAdapterEnabled As Boolean
	Public logTiming As Boolean
	Public consoleOutput As Boolean
	Public logPayload As Boolean
	Public endPoint As String

	Public Sub New()
		
	End Sub
	
End Class
Sub Initialize
	
End Sub












