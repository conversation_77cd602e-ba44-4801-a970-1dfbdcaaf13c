module.exports = {
    modulePaths: ["<rootDir>/node_modules"],
    transform: {
        "^.+\\.tsx?$": "ts-jest",
        ".*\\.(vue)$": "vue-jest"
    },
    testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$",
    testEnvironment: "node",
    moduleFileExtensions: [
        "ts",
        "tsx",
        "js",
        "jsx",
        "json",
        "node",
        "vue"
    ],
    modulePathIgnorePatterns: [
        "<rootDir>/dist/"
    ],
    moduleNameMapper: {
        "\\.(css|less)$": "<rootDir>/config/CSSStub.js",
    },
    verbose: true
};
