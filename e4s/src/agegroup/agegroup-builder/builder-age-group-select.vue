<template>
    <select @change="onSelected" class="generic-select" v-model="ageGroupSelected">
        <option :value="ageGroupDefault" selected>Select</option>
        <option v-for="option in ageGroups" :key="option.id" :value="option">
            {{ ageGroupService.getDropDownLabel(option)}}
        </option>
    </select>
</template>
<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IAgeGroup} from "../agegroup-models";
    import {AgeGroupService} from "../agegroup-service";
    import {Prop, Watch} from "vue-property-decorator";

    @Component({
        name: "builder-age-group-select"
    })
    export default class BuilderAgeGroupSelect extends Vue {
        @Prop({default: () => []}) public ageGroups: IAgeGroup[];
        @Prop({
            default: () => {
                return {
                    id: 0};
                }
            }
        ) public ageGroupDefault: IAgeGroup;

        public ageGroupService: AgeGroupService = new AgeGroupService();
        public ageGroupSelected: IAgeGroup = this.ageGroupService.factoryGetAgeGroup();

        @Watch("ageGroupDefault")
        public onBuilderCompetitionChanged(newValue: IAgeGroup) {
            this.ageGroups.forEach((ag) => {
                if (ag.id === newValue.id) {
                   this.ageGroupSelected = ag;
                }
            });
        }

        public onSelected() {
            if (this.ageGroupSelected && this.ageGroupSelected.id && this.ageGroupSelected.id > 0) {
                this.$emit("onSelected", this.ageGroupSelected);
            }
        }
    }
</script>
