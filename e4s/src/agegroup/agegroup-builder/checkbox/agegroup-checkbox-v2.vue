<template>
  <div class="e4s-flex-column">
    <div class="e4s-flex-row e4s-justify-flex-space-between">
      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <slot name="field-title">
          <FormGenericFieldLabelV2 form-label="Age Groups" />
        </slot>
      </div>

      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <label class="input-checkbox-v2--label" v-if="showDefaultOnlyFilter">
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            :value="true"
            v-model="onlyDefaultAgeGroups"
            v-on:change="init"
          />
          <span
            >Default ONLY
            <span v-if="getDefaultCheckedCount.nonDefault > 0"
              >(and all selected)</span
            ></span
          >
        </label>

        <label class="input-checkbox-v2--label" v-if="showDefaultOnlyFilter">
          <input
            type="radio"
            class="browser-default e4s-input-field e4s-input-field--primary"
            :value="false"
            v-model="onlyDefaultAgeGroups"
            v-on:change="init"
          />
          <span>All</span>
        </label>

        <FieldTextV2
          v-if="showTextFilter"
          v-model="filterText"
          place-holder="Filter age groups"
        />

        <ButtonGenericV2
          style="width: 110px"
          text="Deselect All"
          @click="deselectAllAgeGroups"
          button-type="secondary"
        />
        <ButtonGenericV2
          style="width: 110px"
          text="Select All"
          @click="selectAllAgeGroups"
          button-type="secondary"
        />

        <ButtonGenericV2
          v-if="seeMoreAgeGroupsButton"
          class="e4s-button--auto"
          text="See more age groups"
          @click="showMoreAgeGroups"
        />
      </div>
    </div>

    <div class="dat-e4s-form-input-control--grid--tight">
      <div
        v-for="ageGroup in ageGroupsInternal"
        :key="ageGroup.id"
        :class="
          singleColumn
            ? 'e4s-flex-column e4s-gap--standard'
            : 'e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center'
        "
      >
        <label class="agegroup-checkbox-v2--label">
          <input
            type="checkbox"
            class="
              browser-default
              e4s-input-field
              input-checkbox-v2--input
              agegroup-checkbox-v2--input
            "
            style="accent-color: var(--e4s-button--primary__background)"
            :value="ageGroup"
            v-on:change="ageGroupsSelected"
            v-model="checkedAgeGroups"
          />
          <div
            class="e4s-flex-column"
            :class="
              isDefaultAgeGroup(ageGroup)
                ? 'agegroup-checkbox--default'
                : 'agegroup-checkbox--non-default'
            "
          >
            <div class="e4s-flex-row">
              <div v-text="getName(ageGroup)"></div>
              <div
                class="e4s-flex-row--end e4s-subheader--general"
                v-text="getYears(ageGroup)"
              ></div>
            </div>
            <div
              class="e4s-subheader--general"
              v-text="getDates(ageGroup)"
            ></div>
          </div>
        </label>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {
  AgeGroupService,
  IAgeGroupForAoCodeTypeCount,
} from "../../agegroup-service";
import { IAgeGroup, IAgeGroupCompCoverageModel } from "../../agegroup-models";
import { Prop, Watch } from "vue-property-decorator";
import { CommonService } from "../../../common/common-service";
import { IObjectKeyType } from "../../../common/common-models";
import { IAgeCeidLink } from "../../../builder/buildercompevent/builder-comp-event-models";
import { IBuilderCompetition } from "../../../builder/builder-models";
import { BuilderService } from "../../../builder/builder-service";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import FieldRadioV2 from "../../../common/ui/layoutV2/fields/field-radio-v2.vue";
import {
  simpleClone,
  sortArray,
  uniqueArrayById,
} from "../../../common/common-service-utils";
import { AO_CODE } from "../../../common/ui/athletic-org/athletic-org-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";
import FormGenericSectionTitleOnlyV2 from "../../../common/ui/layoutV2/form/form-generic-section-title-only-v2.vue";
import FormGenericFieldLabelV2 from "../../../common/ui/layoutV2/form/form-generic--field-label-v2.vue";
import InputCheckboxV2 from "../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import AgegroupItem from "../agegroup-item/AgegroupItem.vue";
import { format, parse } from "date-fns";

const ageGroupService: AgeGroupService = new AgeGroupService();
const commonService: CommonService = new CommonService();
const builderService: BuilderService = new BuilderService();

@Component({
  name: "ageGroupCheckBoxV2",
  components: {
    AgegroupItem,
    InputCheckboxV2,
    FormGenericFieldLabelV2,
    FormGenericSectionTitleOnlyV2,
    FieldTextV2,
    ButtonGenericV2,
    FieldRadioV2,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class AgeGroupCheckBox extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroups: IAgeGroup[];
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroupsDefault: IAgeGroup[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageCeidLinks: IAgeCeidLink[];

  @Prop({
    default: () => {
      return builderService.factoryGetBuilder({});
    },
  })
  public readonly builderCompetition: IBuilderCompetition;

  @Prop({
    default: false,
  })
  public readonly allowDefaultToShowAllAgeGroups: boolean;

  @Prop({
    default: false,
  })
  public readonly showTextFilter: boolean;

  @Prop({
    default: true,
  })
  public readonly showDefaultOnlyFilter: boolean;

  @Prop({
    default: false,
  })
  public readonly singleColumn: boolean;

  @Prop({
    default: false,
  })
  public readonly seeMoreAgeGroupsButton: boolean;

  public ageGroupService = ageGroupService;
  public ageGroupsInternal: IAgeGroup[] = [];
  public checkedAgeGroups: IAgeGroup[] = [];
  public checkedAgeGroupsObject: IObjectKeyType<IAgeGroup> = {};
  public ageCeidLinksObject: IObjectKeyType<IAgeCeidLink> = {};
  public filterText: string = "";

  public onlyDefaultAgeGroups = false;

  public currentAoCode: AO_CODE = "";

  public created() {
    // if (this.allowDefaultToShowAllAgeGroups) {
    //   this.onlyDefaultAgeGroups = false;
    // }

    this.onlyDefaultAgeGroups = !this.allowDefaultToShowAllAgeGroups;

    this.init();
  }

  @Watch("ageGroups")
  public onAgeGroupsChanged(newValue: IAgeGroup[]) {
    this.init();
  }

  @Watch("ageGroupsDefault")
  public onAgeGroupsDefaultChanged(newValue: IAgeGroup[]) {
    this.init();
  }

  @Watch("checkedAgeGroups")
  public onCheckedAgeGroupsChanged(newValue: IAgeGroup[]) {
    this.checkedAgeGroupsObject = commonService.convertArrayToObject(
      "id",
      newValue
    );
  }

  @Watch("ageCeidLinks")
  public onAgeCeidLinksChanged(newValue: IAgeCeidLink[]) {
    this.init();
  }

  public init() {
    let ageGroups: IAgeGroup[] = [];

    this.currentAoCode = this.ageGroupService.getAoCodeFromTheme(
      this.configApp.theme
    );

    if (this.onlyDefaultAgeGroups) {
      ageGroups = sortArray(
        "minAge",
        uniqueArrayById(
          ageGroupService
            .filterDefaultAgeGroups(this.ageGroups, this.configApp.theme)
            .concat(simpleClone(this.checkedAgeGroups)),
          "id"
        )
      );
    } else {
      ageGroups = R.clone(this.ageGroups);
    }

    // this.ageGroupsInternal = R.clone(this.ageGroups);
    this.ageGroupsInternal = R.clone(ageGroups);
    this.checkedAgeGroups = ageGroupService.getAgeGroupsFromDefaults(
      this.ageGroups,
      this.ageGroupsDefault
    ) as IAgeGroup[];
    this.ageCeidLinksObject = commonService.convertArrayToObject(
      "agid",
      this.ageCeidLinks
    );
    this.checkedAgeGroupsObject = commonService.convertArrayToObject(
      "id",
      this.checkedAgeGroups
    );
  }

  public getName(ageGroup: IAgeGroup) {
    return ageGroup.name + (this.isAdmin ? " (" + ageGroup.id + ")" : "");
  }

  public getLabel(ageGroup: IAgeGroup) {
    return ageGroupService.getCheckBoxLabel(
      ageGroup,
      this.builderCompetition,
      this.isAdmin
    );
  }

  public getYears(ageGroup: IAgeGroup | IAgeGroupCompCoverageModel) {
    if ((ageGroup as IAgeGroupCompCoverageModel).fromDate) {
      return "(" + ageGroup.minAge + "-" + ageGroup.maxAge + ")";
    }
    return "";
  }

  public getDates(ageGroup: IAgeGroup | IAgeGroupCompCoverageModel) {
    if ((ageGroup as IAgeGroupCompCoverageModel).fromDate) {
      const ageGroupCompCoverageModel = ageGroup as IAgeGroupCompCoverageModel;
      return (
        format(parse(ageGroupCompCoverageModel.fromDate), "Do MMM YYYY") +
        " to " +
        format(parse(ageGroupCompCoverageModel.toDate), "Do MMM YYYY")
      );
    }
    return "";
  }

  public doesAgeGroupMatchFilter(ageGroup: IAgeGroup): boolean {
    if (this.filterText.length > 0) {
      const lengthFilter = this.filterText.length;
      const filterText = this.filterText.toLowerCase();
      // console.log("ageGroupsFilter() event-teams-filter: " + filterText);
      const ageGroupLabel = this.getLabel(ageGroup);
      const ageGroupLabelBeginsWith = ageGroupLabel.slice(0, lengthFilter);
      const ageGroupLabelContains = ageGroupLabel.indexOf(filterText);
      const maxBeginsWith = ageGroup.maxAge.toString().slice(0, lengthFilter);
      const minBeginsWith = ageGroup.minAge.toString().slice(0, lengthFilter);

      return (
        ageGroupLabelBeginsWith.toLowerCase() === filterText ||
        maxBeginsWith === filterText ||
        minBeginsWith === filterText ||
        ageGroupLabelContains > 0
      );
    }
    return true;
  }

  public getCheckBoxClass(ageGroup: IAgeGroup): string {
    const cssClasses: string[] = [];

    if (this.filterText.length > 0 && this.doesAgeGroupMatchFilter(ageGroup)) {
      cssClasses.push("check-box-highlight");
    }

    const ageCeidLink: IAgeCeidLink = this.ageCeidLinksObject[ageGroup.id + ""];
    if (ageCeidLink) {
      if (
        this.ageGroupService.isDefaultAgeGroup(ageGroup, this.currentAoCode)
      ) {
        if (ageCeidLink.crud === "C") {
          cssClasses.push("check-box-create");
        }
        if (ageCeidLink.crud === "U") {
          cssClasses.push("check-box-update");
        }
        if (ageCeidLink.crud === "D") {
          cssClasses.push("check-box-delete");
        }
      } else {
        cssClasses.push("agegroup-checkbox--non-default");
      }
    }

    return cssClasses.join(" ");
  }

  public selectAllAgeGroups() {
    const allMatchingFilter: IAgeGroup[] = this.ageGroupsInternal.filter(
      (ageGroup: IAgeGroup) => {
        return this.doesAgeGroupMatchFilter(ageGroup);
      }
    );
    this.checkedAgeGroups = commonService.uniqueArrayById([
      ...allMatchingFilter,
      ...this.checkedAgeGroups,
    ] as IAgeGroup[]) as IAgeGroup[];
    this.ageGroupsSelected();
  }

  public deselectAllAgeGroups() {
    this.checkedAgeGroups = [];
    this.ageGroupsSelected();
  }

  public get getDefaultCheckedCount(): IAgeGroupForAoCodeTypeCount {
    return this.ageGroupService.getAgeGroupCounts(
      this.checkedAgeGroups,
      this.configApp.theme
    );
  }

  public isDefaultAgeGroup(ageGroup: IAgeGroup): boolean {
    return this.ageGroupService.isDefaultAgeGroup(ageGroup, this.currentAoCode);
  }

  public ageGroupsSelected() {
    this.$emit("onSelected", R.clone(this.checkedAgeGroups));
  }

  public showMoreAgeGroups() {
    this.$emit("showMoreAgeGroups");
  }
}
</script>

<style scoped>
.agegroup-checkbox-v2--label {
  display: flex;
  /*justify-content: center;*/
  align-items: flex-start;
  //vertical-align: top;
  word-wrap: break-word;
  color: black;
  font-size: 1em;
  gap: var(--e4s-gap--standard);
}

.agegroup-checkbox-v2--input {
  height: 16px;
}

.agegroup-checkbox--label {
  font-size: 1em;
  color: #0f0f0f;
}

.check-box-highlight {
  font-weight: 700;
  /*color: black;*/
  background-color: yellow;
}

.check-box-create {
  /*font-weight: 700;*/
  color: green;
}

.check-box-update {
  /*font-weight: 700;*/
  color: blue;
}

.check-box-delete {
  /*font-weight: 700;*/
  color: red;
}

.agegroup-checkbox--standard {
  color: black;
}

.agegroup-checkbox--default {
}

.agegroup-checkbox--non-default {
  color: var(--e4s-info-section--warn__text-color);
}
</style>
