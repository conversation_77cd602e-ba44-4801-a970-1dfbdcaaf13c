<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-justify-flex-row-vert-center
          "
        >
          <FieldRadioV2
            :option-value="true"
            v-model="onlyDefaultAgeGroups"
            :label="
              'Default ONLY' +
              (getDefaultCheckedCount.nonDefault > 0
                ? ' (and all selected)'
                : '')
            "
            @onChanged="init"
          />

          <FieldRadioV2
            :option-value="false"
            v-model="onlyDefaultAgeGroups"
            label="All"
            @onChanged="init"
          />

          <input
            class="browser-default"
            type="text"
            v-model="filterText"
            placeholder="Filter age groups"
          />
          <span v-text="checkedAgeGroups.length"></span> selected (
          Default:<span v-text="getDefaultCheckedCount.default"></span>
          <span class="agegroup-checkbox--non-default"
            >Not default:<span v-text="getDefaultCheckedCount.nonDefault"></span
          ></span>
          )

          <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
            <ButtonGenericV2
              class="e4s-button--100"
              @click="deselectAllAgeGroups"
              text="Deselect All"
            />
            <ButtonGenericV2
              class="e4s-button--100"
              @click="selectAllAgeGroups"
              text="Select All"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="dat-e4s-form-input-control--grid--tight">
          <div
            v-for="ageGroup in ageGroupsInternal"
            :key="ageGroup.id"
            class="e4s-flex-column e4s-gap--standard"
            :class="
              singleColumn
                ? 'e4s-flex-column e4s-gap--standard'
                : 'e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center'
            "
          >
            <label class="agegroup-checkbox-v2--label">
              <input
                type="checkbox"
                class="
                  browser-default
                  e4s-input-field
                  input-checkbox-v2--input
                  agegroup-checkbox-v2--input
                "
                style="accent-color: var(--e4s-button--primary__background)"
                :value="ageGroup"
                v-on:change="ageGroupsSelected"
                v-model="checkedAgeGroups"
              />
              <div
                class="e4s-flex-column"
                :class="
                  isDefaultAgeGroup(ageGroup)
                    ? 'agegroup-checkbox--default'
                    : 'agegroup-checkbox--non-default'
                "
              >
                <div class="e4s-flex-row">
                  <div v-text="getName(ageGroup)"></div>
                  <div
                    class="e4s-flex-row--end e4s-subheader--general"
                    v-text="getYears(ageGroup)"
                  ></div>
                </div>
                <div
                  class="e4s-subheader--general"
                  v-text="getDates(ageGroup)"
                ></div>
              </div>
            </label>

            <!--          <label class="agegroup-checkbox-v2&#45;&#45;label">-->
            <!--            <input-->
            <!--              class="-->
            <!--                browser-default-->
            <!--                e4s-input-field-->
            <!--                input-checkbox-v2&#45;&#45;input-->
            <!--                agegroup-checkbox-v2&#45;&#45;input-->
            <!--              "-->
            <!--              type="checkbox"-->
            <!--              :value="ageGroup"-->
            <!--              v-on:change="ageGroupsSelected"-->
            <!--              v-model="checkedAgeGroups"-->
            <!--            />-->
            <!--            <span-->
            <!--              class="agegroup-checkbox&#45;&#45;standard"-->
            <!--              :class="-->
            <!--                isDefaultAgeGroup(ageGroup)-->
            <!--                  ? 'agegroup-checkbox&#45;&#45;default'-->
            <!--                  : 'agegroup-checkbox&#45;&#45;non-default'-->
            <!--              "-->
            <!--            >-->
            <!--              <i-->
            <!--                title="Under development, will show dates."-->
            <!--                class="tiny material-icons"-->
            <!--                >more_horiz</i-->
            <!--              >-->
            <!--              <span-->
            <!--                :class="getCheckBoxClass(ageGroup)"-->
            <!--                v-text="getLabel(ageGroup)"-->
            <!--              ></span>-->
            <!--            </span>-->
            <!--          </label>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import {
  AgeGroupService,
  IAgeGroupForAoCodeTypeCount,
} from "../../agegroup-service";
import {
  IAgeGroup,
  IAgeGroupBase,
  IAgeGroupCompCoverageModel,
} from "../../agegroup-models";
import { Prop, Watch } from "vue-property-decorator";
import { CommonService } from "../../../common/common-service";
import { IObjectKeyType } from "../../../common/common-models";
import { IAgeCeidLink } from "../../../builder/buildercompevent/builder-comp-event-models";
import { IBuilderCompetition } from "../../../builder/builder-models";
import { BuilderService } from "../../../builder/builder-service";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import FieldRadioV2 from "../../../common/ui/layoutV2/fields/field-radio-v2.vue";
import {
  simpleClone,
  sortArray,
  uniqueArrayById,
} from "../../../common/common-service-utils";
import { AO_CODE } from "../../../common/ui/athletic-org/athletic-org-models";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { format, parse } from "date-fns";

const ageGroupService: AgeGroupService = new AgeGroupService();
const commonService: CommonService = new CommonService();
const builderService: BuilderService = new BuilderService();

@Component({
  name: "age-group-check-box",
  components: { ButtonGenericV2, FieldRadioV2 },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class AgeGroupCheckBox extends Vue {
  public readonly isAdmin: boolean;
  public readonly configApp: IConfigApp;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroups: IAgeGroup[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroupsDefault: IAgeGroup[] | IAgeGroupBase[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageCeidLinks: IAgeCeidLink[];

  @Prop({
    default: () => {
      return builderService.factoryGetBuilder({});
    },
  })
  public readonly builderCompetition: IBuilderCompetition;

  @Prop({
    default: false,
  })
  public readonly allowDefaultToShowAllAgeGroups: boolean;

  @Prop({
    default: false,
  })
  public readonly singleColumn: boolean;

  public ageGroupService = ageGroupService;
  public ageGroupsInternal: IAgeGroup[] = [];
  public checkedAgeGroups: IAgeGroup[] = [];
  public checkedAgeGroupsObject: IObjectKeyType<IAgeGroup> = {};
  public ageCeidLinksObject: IObjectKeyType<IAgeCeidLink> = {};
  public filterText: string = "";

  public onlyDefaultAgeGroups = true;

  public currentAoCode: AO_CODE = "";

  public created() {
    if (this.allowDefaultToShowAllAgeGroups) {
      this.onlyDefaultAgeGroups = false;
    }

    this.init();
  }

  @Watch("ageGroups")
  public onAgeGroupsChanged() {
    this.init();
  }

  @Watch("ageGroupsDefault")
  public onAgeGroupsDefaultChanged() {
    this.init();
  }

  @Watch("checkedAgeGroups")
  public onCheckedAgeGroupsChanged(newValue: IAgeGroup[]) {
    this.checkedAgeGroupsObject = commonService.convertArrayToObject(
      "id",
      newValue
    );
  }

  @Watch("ageCeidLinks")
  public onAgeCeidLinksChanged() {
    this.init();
  }

  public init() {
    let ageGroups: IAgeGroup[];

    this.currentAoCode = this.ageGroupService.getAoCodeFromTheme(
      this.configApp.theme
    );

    if (this.onlyDefaultAgeGroups) {
      ageGroups = sortArray(
        "minAge",
        uniqueArrayById(
          ageGroupService
            .filterDefaultAgeGroups(this.ageGroups, this.configApp.theme)
            .concat(simpleClone(this.checkedAgeGroups)),
          "id"
        )
      );
    } else {
      ageGroups = R.clone(this.ageGroups);
    }

    // const ageGroups = this.onlyDefaultAgeGroups
    //   ? ageGroupService.filterDefaultAgeGroups(
    //       this.ageGroups,
    //       this.configApp.theme
    //     )
    //   : R.clone(this.ageGroups);

    // this.ageGroupsInternal = R.clone(this.ageGroups);
    this.ageGroupsInternal = R.clone(ageGroups);
    this.checkedAgeGroups = ageGroupService.getAgeGroupsFromDefaults(
      this.ageGroups,
      this.ageGroupsDefault
    ) as IAgeGroup[];
    this.ageCeidLinksObject = commonService.convertArrayToObject(
      "agid",
      this.ageCeidLinks
    );
    this.checkedAgeGroupsObject = commonService.convertArrayToObject(
      "id",
      this.checkedAgeGroups
    );
  }

  public doesAgeGroupMatchFilter(ageGroup: IAgeGroup): boolean {
    if (this.filterText.length > 0) {
      const lengthFilter = this.filterText.length;
      const filterText = this.filterText.toLowerCase();
      // console.log("ageGroupsFilter() event-teams-filter: " + filterText);
      const ageGroupLabel = this.getLabel(ageGroup);
      const ageGroupLabelBeginsWith = ageGroupLabel.slice(0, lengthFilter);
      const ageGroupLabelContains = ageGroupLabel.indexOf(filterText);
      const maxBeginsWith = ageGroup.maxAge.toString().slice(0, lengthFilter);
      const minBeginsWith = ageGroup.minAge.toString().slice(0, lengthFilter);

      return (
        ageGroupLabelBeginsWith.toLowerCase() === filterText ||
        maxBeginsWith === filterText ||
        minBeginsWith === filterText ||
        ageGroupLabelContains > 0
      );
    }
    return true;
  }

  public getCheckBoxClass(ageGroup: IAgeGroup): string {
    const cssClasses: string[] = [];

    if (this.filterText.length > 0 && this.doesAgeGroupMatchFilter(ageGroup)) {
      cssClasses.push("check-box-highlight");
    }

    const ageCeidLink: IAgeCeidLink = this.ageCeidLinksObject[ageGroup.id + ""];
    if (ageCeidLink) {
      if (
        this.ageGroupService.isDefaultAgeGroup(ageGroup, this.currentAoCode)
      ) {
        if (ageCeidLink.crud === "C") {
          cssClasses.push("check-box-create");
        }
        if (ageCeidLink.crud === "U") {
          cssClasses.push("check-box-update");
        }
        if (ageCeidLink.crud === "D") {
          cssClasses.push("check-box-delete");
        }
      } else {
        cssClasses.push("agegroup-checkbox--non-default");
      }
    }

    return cssClasses.join(" ");
  }

  public selectAllAgeGroups() {
    const allMatchingFilter: IAgeGroup[] = this.ageGroupsInternal.filter(
      (ageGroup: IAgeGroup) => {
        return this.doesAgeGroupMatchFilter(ageGroup);
      }
    );
    this.checkedAgeGroups = commonService.uniqueArrayById([
      ...allMatchingFilter,
      ...this.checkedAgeGroups,
    ] as IAgeGroup[]) as IAgeGroup[];
    this.ageGroupsSelected();
  }

  public deselectAllAgeGroups() {
    this.checkedAgeGroups = [];
    this.ageGroupsSelected();
  }

  public get getDefaultCheckedCount(): IAgeGroupForAoCodeTypeCount {
    return this.ageGroupService.getAgeGroupCounts(
      this.checkedAgeGroups,
      this.configApp.theme
    );
  }

  public isDefaultAgeGroup(ageGroup: IAgeGroup): boolean {
    return this.ageGroupService.isDefaultAgeGroup(ageGroup, this.currentAoCode);
  }

  public ageGroupsSelected() {
    this.$emit("onSelected", R.clone(this.checkedAgeGroups));
  }

  public getName(ageGroup: IAgeGroup) {
    return ageGroup.name + (this.isAdmin ? " (" + ageGroup.id + ")" : "");
  }

  // public getLabel(ageGroup: IAgeGroup): string {
  //   return ageGroupService.getCheckBoxLabel(
  //     ageGroup,
  //     this.builderCompetition,
  //     this.isAdmin
  //   );
  // }

  public getLabel(ageGroup: IAgeGroup) {
    return ageGroupService.getCheckBoxLabel(
      ageGroup,
      this.builderCompetition,
      this.isAdmin
    );
  }

  public getYears(ageGroup: IAgeGroup | IAgeGroupCompCoverageModel) {
    if ((ageGroup as IAgeGroupCompCoverageModel).fromDate) {
      return "(" + ageGroup.minAge + "-" + ageGroup.maxAge + ")";
    }
    return "";
  }

  public getDates(ageGroup: IAgeGroup | IAgeGroupCompCoverageModel) {
    if ((ageGroup as IAgeGroupCompCoverageModel).fromDate) {
      const ageGroupCompCoverageModel = ageGroup as IAgeGroupCompCoverageModel;
      return (
        format(parse(ageGroupCompCoverageModel.fromDate), "Do MMM YYYY") +
        " to " +
        format(parse(ageGroupCompCoverageModel.toDate), "Do MMM YYYY")
      );
    }
    return "";
  }
}
</script>

<style scoped>
.check-box-highlight {
  font-weight: 700;
  /*color: black;*/
  background-color: yellow;
}

.check-box-create {
  /*font-weight: 700;*/
  color: green;
}

.check-box-update {
  /*font-weight: 700;*/
  color: blue;
}

.check-box-delete {
  /*font-weight: 700;*/
  color: red;
}

.agegroup-checkbox--standard {
  color: black;
}

.agegroup-checkbox--default {
}

.agegroup-checkbox--non-default {
  color: var(--e4s-info-section--warn__text-color);
}
</style>
