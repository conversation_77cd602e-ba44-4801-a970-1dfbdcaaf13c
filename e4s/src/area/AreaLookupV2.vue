<template>
  <CommonTypeAhead
    :default-object="valueInternal"
    :data-function="search"
    :is-modal="isModal"
    :is-disabled="isDisabled"
    :reset-input-on-selected="resetInputOnSelected"
    v-on:selected="onChanged"
    v-on:reset="reset"
  >
    <div slot-scope="{ result }">
      <div v-text="getOptionDisplayValue(result)"></div>
    </div>
  </CommonTypeAhead>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import CommonTypeAhead from "../common/ui/type-ahead/common-type-ahead.vue";
import { IArea } from "./area-models";
import { AreaData } from "./area-data";
import { simpleClone } from "../common/common-service-utils";
import { IListParams } from "../common/resource/resource-service";
import { useConfigController } from "../config/useConfigStore";

export default defineComponent({
  name: "AreaLookupV2",
  components: { CommonTypeAhead },
  props: {
    value: {
      type: Object as PropType<IArea>,
      default: () => {
        return {
          id: 0,
          name: "",
        };
      },
    },
    isDisabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    isModal: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    resetInputOnSelected: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  setup(
    props: {
      value: IArea;
      isDisabled: boolean;
      isModal: boolean;
      resetInputOnSelected: boolean;
    },
    context: SetupContext
  ) {
    const valueInternal = ref(props.value);
    const areaData = new AreaData();
    const configController = useConfigController();

    // const state = ref({
    //   isLoading: false,
    //   areas: [] as IArea[],
    // });

    watch(
      () => props.value,
      (newValue: IArea) => {
        valueInternal.value = simpleClone(newValue);
      },
      {
        immediate: true,
      }
    );

    function search(searchTerm: string) {
      const listParams = {
        startswith: searchTerm,
        pagenumber: 1,
        pagesize: 20,
        sortkey: "name",
      } as IListParams;

      return areaData.list(listParams);
    }

    function getOptionDisplayValue(area: IArea) {
      console.log(
        "getOptionDisplayValue, configController.isAdmin",
        configController.isAdmin
      );
      return (
        area.name + (configController.isAdmin.value ? " (" + area.id + ")" : "")
      );
    }

    function onChanged(selectedValue: IArea) {
      valueInternal.value = selectedValue;
      context.emit("input", valueInternal.value);
      context.emit("onChange", valueInternal.value);
    }

    function reset() {
      const areaDefault: IArea = {
        id: 0,
        name: "",
      };

      if (props.isModal) {
        //  If modal, the expectation is it just changes data in modal, not underlying
        //  component...
        valueInternal.value = areaDefault;
        return;
      }

      //  But if area type ahead is on form, reset the model.
      context.emit("input", areaDefault);
      context.emit("onChange", areaDefault);
    }

    return {
      valueInternal,
      search,
      onChanged,
      reset,
      getOptionDisplayValue,
      configController,
    };
  },
});
</script>
