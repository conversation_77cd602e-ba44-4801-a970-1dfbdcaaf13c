@import url("https://fonts.googleapis.com/css2?family=Cabin:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap");

:root {
  /*************************************************************************************************************************************
    Colours
    *************************************************************************************************************************************/

  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;

  --grey-50: #f9fafb;
  --grey-100: #f3f4f6;
  --grey-200: #e5e7eb;
  --grey-300: #d1d5db;
  --grey-400: #9ca3af;
  --grey-500: #6b7280;
  --grey-600: #4b5563;
  --grey-700: #374151;
  --grey-800: #1f2937;
  --grey-900: #111827;

  --zinc-50: #fafafa;
  --zinc-100: #f4f4f5;
  --zinc-200: #e4e4e7;
  --zinc-300: #d4d4d8;
  --zinc-400: #a1a1aa;
  --zinc-500: #71717a;
  --zinc-600: #52525b;
  --zinc-700: #3f3f46;
  --zinc-800: #27272a;
  --zinc-900: #18181b;

  --neutral-0: #ffffff;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  --stone-50: #fafaf9;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-300: #d6d3d1;
  --stone-400: #a8a29e;
  --stone-500: #78716c;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;
  --stone-900: #1c1917;

  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;

  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;

  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;

  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;

  --lime-50: #f7fee7;
  --lime-100: #ecfccb;
  --lime-200: #d9f99d;
  --lime-300: #bef264;
  --lime-400: #a3e635;
  --lime-500: #84cc16;
  --lime-600: #65a30d;
  --lime-700: #4d7c0f;
  --lime-800: #3f6212;
  --lime-900: #365314;

  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;

  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;

  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;

  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;

  --sky-00: #f0f9ff;
  --sky-00: #e0f2fe;
  --sky-00: #bae6fd;
  --sky-00: #7dd3fc;
  --sky-00: #38bdf8;
  --sky-00: #0ea5e9;
  --sky-00: #0284c7;
  --sky-00: #0369a1;
  --sky-00: #075985;
  --sky-00: #0c4a6e;

  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;

  --indigo-50: #eef2ff;
  --indigo-100: #e0e7ff;
  --indigo-200: #c7d2fe;
  --indigo-300: #a5b4fc;
  --indigo-400: #818cf8;
  --indigo-500: #6366f1;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --indigo-800: #3730a3;
  --indigo-900: #312e81;

  --violet-50: #f5f3ff;
  --violet-100: #ede9fe;
  --violet-200: #ddd6fe;
  --violet-300: #c4b5fd;
  --violet-400: #a78bfa;
  --violet-500: #8b5cf6;
  --violet-600: #7c3aed;
  --violet-700: #6d28d9;
  --violet-800: #5b21b6;
  --violet-900: #4c1d95;

  --purple-00: #faf5ff;
  --purple-00: #f3e8ff;
  --purple-00: #e9d5ff;
  --purple-00: #d8b4fe;
  --purple-00: #c084fc;
  --purple-00: #a855f7;
  --purple-00: #9333ea;
  --purple-00: #7e22ce;
  --purple-00: #6b21a8;
  --purple-00: #581c87;

  --fuchsia-50: #fdf4ff;
  --fuchsia-100: #fae8ff;
  --fuchsia-200: #f5d0fe;
  --fuchsia-300: #f0abfc;
  --fuchsia-400: #e879f9;
  --fuchsia-500: #d946ef;
  --fuchsia-600: #c026d3;
  --fuchsia-700: #a21caf;
  --fuchsia-800: #86198f;
  --fuchsia-900: #701a75;

  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;

  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;

  /*************************************************************************************************************************************
    Typography (naming convention: e4s-typography-type--hierarchy
    *************************************************************************************************************************************/

  --e4s-aai-orange: #f7921e;

  --e4s-header--100: normal bold 2rem "Cabin", "Open Sans", Arial;
  --e4s-header--200: normal bold 1.75rem "Cabin", "Open Sans", Arial;
  --e4s-header--300: normal bold 1.5rem "Cabin", "Open Sans", Arial;
  --e4s-header--400: normal bold 1.25rem "Cabin", "Open Sans", Arial;
  --e4s-header--500: normal bold 1rem "Cabin", "Open Sans", Arial;

  --e4s-subheader--100: normal 600 1.25rem "Cabin", "Open Sans", Arial;
  --e4s-subheader--200: normal 600 1rem "Cabin", "Open Sans", Arial;
  --e4s-subheader--300: normal 600 0.75rem "Cabin", "Open Sans", Arial;
  --e4s-subheader--400: normal 600 0.625rem "Cabin", "Open Sans", Arial;

  --e4s-header-subheader--padding: 0 0 8px 0;

  --e4s-body--100: normal 400 1rem "Cabin", "Open Sans", Arial;
  --e4s-body--200: normal 400 0.75rem "Cabin", "Open Sans", Arial;
  --e4s-body--300: normal 400 0.625rem "Cabin", "Open Sans", Arial;
  --e4s-body--error: var(--red-700);
  --e4s-body--backgroundColor: #f7fbff;

  --e4s-hyperlink--100: normal 600 1rem "Cabin", "Open Sans", Arial;
  --e4s-hyperlink--primary__text-color: var(--blue-900);
  --e4s-hyperlink--primary__active-text-color: var(--blue-700);
  --e4s-hyperlink--ia-primary__text-color: var(--teal-800);
  --e4s-hyperlink--ia-primary__active-text-color: var(--teal-700);

  /*************************************************************************************************************************************
    Component styles (naming convention: e4s-component-type--hierarchy__property
    *************************************************************************************************************************************/

  /* Navigation */
  /*--e4s-navigation-bar__height: 84px;*/
  --e4s-navigation-bar__height: 64px;
  /*--e4s-navigation-bar__height-mobile: 60px;*/
  --e4s-navigation-bar--primary__background: var(--blue-800);
  --e4s-navigation-bar--ia-primary__background: var(--teal-800);

  --e4s-navigation-link--primary__text-color: var(--neutral-0);
  --e4s-navigation-link--primary__hover-background: var(--blue-900);
  --e4s-navigation-link--ia-primary__text-color: var(--neutral-0);
  --e4s-navigation-link--ia-primary__hover-background: var(--teal-900);

  --e4s-sub-nav__height: 125px;
  --e4s-sub-nav--primary__background: var(--slate-800);
  --e4s-sub-nav--primary__text-color: var(--neutral-0);
  --e4s-sub-nav--ia-primary__background: var(--teal-900);
  --e4s-sub-nav--ia-primary__text-color: var(--neutral-0);

  --e4s-form-nav-link-indicator__height: 16px;
  --e4s-form-nav-link-indicator__width: 16px;
  --e4s-form-nav-link-indicator__border-radius: 50px;
  --e4s-form-nav-link-indicator__background: var(--slate-300);
  --e4s-form-nav-link-indicator__text-color: var(--slate-500);
  --e4s-form-nav-link-indicator--primary__text-color: var(--neutral-0);
  --e4s-form-nav-link-indicator--primary__background: var(--blue-800);
  --e4s-form-nav-link-indicator--ia-primary__background: var(--orange-600);
  --e4s-form-nav-link-indicator--ia-primary__text-color: var(--neutral-0);

  --e4s-form-nav-link__text-color: var(--slate-800);
  --e4s-form-nav-link--primary__text-color: var(--blue-800);
  --e4s-form-nav-link--ia-primary__text-color: var(--teal-800);

  /* Footer */
  --e4s-footer__height: 60px;
  --e4s-footer__margin: 10px 0 0 0;
  --e4s-footer--primary__background: var(--slate-900);
  --e4s-footer--ia-primary__background: var(--teal-900);

  --e4s-footer--primary__text-color: var(--neutral-0);
  --e4s-footer--ia-primary__text-color: var(--neutral-0);

  /* Badge Counts */
  --e4s-badge__padding: 2px 4px;
  --e4s-badge__border-radius: 4px;
  --e4s-badge--navigation--primary__background: var(--neutral-0);
  --e4s-badge--navigation--primary__text-color: var(--slate-800);
  --e4s-badge--navigation--ia-primary__background: var(--neutral-0);
  --e4s-badge--navigation--ia-primary__text-color: var(--slate-800);

  /* Cards */
  /*changed*/
  --e4s-card--primary__padding: 8px;
  --e4s-card--primary__border: 1px solid;
  --e4s-card--primary__border-color: var(--slate-400);
  --e4s-card--primary__border-radius: 8px;
  --e4s-card--primary__background: linear-gradient(
    to top,
    var(--slate-50) -100%,
    var(--neutral-0) 30%
  );

  --e4s-card-thin--primary__padding: 8px;
  --e4s-card-superthin__padding: 4px;

  /* Pills */
  --e4s-status-pill__padding: 4px 8px;
  --e4s-status-pill__border-radius: 50px;
  --e4s-status-pill--success__background: var(--teal-50);
  --e4s-status-pill--success__text-color: var(--teal-800);
  --e4s-status-pill--success__border-color: var(--teal-100);
  --e4s-status-pill--alert__background: var(--orange-50);
  --e4s-status-pill--alert__text-color: var(--orange-600);
  --e4s-status-pill--error__background: var(--red-50);
  --e4s-status-pill--error__text-color: var(--red-600);

  /* Input fields */
  --e4s-input-field__height: 36px;
  --e4s-input-field__border: 1px solid;
  --e4s-input-field__border-radius: 4px;
  --e4s-input-field__border-color: var(--grey-300);
  --e4s-input-field__background: var(--neutral-0);
  --e4s-input-field__background-disabled: var(--grey-100);
  --e4s-input-field__text-color: var(--slate-800);
  --e4s-input-field__text-color-disabled: var(--grey-100);
  --e4s-input-field__placeholder-text-color: var(--slate-900);
  --e4s-input-field--error__border-color: var(--red-700);
  --e4s-input-field--error__text-color: var(--red-700);
  --e4s-input-field--error__caret-color: var(--red-700);
  --e4s-input-field--primary__caret-color: var(--blue-800);
  --e4s-input-field--primary__active-border-color: var(--blue-800);
  --e4s-input-field--ia-primary__active-border-color: var(--orange-600);
  --e4s-input-field--ia-primary__caret-color: var(--orange-600);

  /* Info */
  --e4s-info-section--success__background: var(--teal-50);
  --e4s-info-section--success__text-color: var(--teal-900);
  /*--e4s-info-section--success__border-color: var(--red-700);*/

  --e4s-info-section--info__background: var(--blue-50);
  --e4s-info-section--info__text-color: var(--blue-800);
  --e4s-info-section--info__border-color: var(--blue-300);

  --e4s-info-section--warn__background: var(--orange-50);
  --e4s-info-section--warn__text-color: var(--orange-800);
  --e4s-info-section--warn__border-color: var(--orange-200);

  --e4s-info-section--error__background: var(--red-50);
  --e4s-info-section--error__text-color: var(--red-800);
  --e4s-info-section--error__border-color: var(--red-400);

  /* Buttons */
  --e4s-button__height: 36px;
  --e4s-button__padding: 8px 12px;
  --e4s-button__border: none;
  --e4s-button__border-radius: 4px;

  --e4s-button--primary__background: var(--blue-800);
  --e4s-button--primary__hover-background: var(--blue-700);
  --e4s-button--primary__active-background: var(--blue-900);
  --e4s-button--primary__text-color: var(--neutral-0);
  --e4s-button--primary__disabled-background: var(--grey-300);
  --e4s-button--primary__disabled-text-color: var(--grey-400);

  --e4s-button--secondary__border: 1px solid;
  --e4s-button--secondary__border-color: var(--blue-400);
  --e4s-button--secondary__background: var(--blue-200);
  --e4s-button--secondary__hover-background: var(--blue-400);
  --e4s-button--secondary__active-background: var(--blue-400);
  --e4s-button--secondary__hover-border-color: var(--blue-600);
  --e4s-button--secondary__active-border-color: var(--blue-600);
  --e4s-button--secondary__text-color: var(--slate-800);
  --e4s-button--secondary__disabled-background: var(--grey-300);
  --e4s-button--secondary__disabled-border-color: var(--grey-300);
  --e4s-button--secondary__disabled-text-color: var(--grey-400);

  --e4s-button--tertiary__border: 1px solid;
  /*--e4s-button--tertiary__border-color: transparent;*/
  /*--e4s-button--tertiary__background: var(--slate-100);*/
  /*border #a0caf5 back #dceaf7*/
  --e4s-button--tertiary__border-color: var(--blue-100);
  --e4s-button--tertiary__background: var(--blue-50);

  --e4s-button--tertiary__hover-background: var(--slate-100);
  --e4s-button--tertiary__active-background: var(--slate-300);
  --e4s-button--tertiary__hover-border-color: var(--slate-300);
  --e4s-button--tertiary__active-border-color: var(--slate-300);
  --e4s-button--tertiary__text-color: var(--slate-800);
  --e4s-button--tertiary__disabled-background: var(--grey-300);
  --e4s-button--tertiary__disabled-text-color: var(--grey-400);

  --e4s-button--ia-primary__background: var(--teal-800);
  --e4s-button--ia-primary__hover-background: var(--teal-700);
  --e4s-button--ia-primary__active-background: var(--teal-900);
  --e4s-button--ia-primary__text-color: var(--neutral-0);
  --e4s-button--ia-primary__disabled-background: var(--grey-300);
  --e4s-button--ia-primary__disabled-text-color: var(--grey-400);

  --e4s-button--ia-secondary__border: 1px solid;
  --e4s-button--ia-secondary__border-color: #78bfb9; /*var(--teal-600);*/ /*#78bfb9*/
  --e4s-button--ia-secondary__background: #b2e1d8; /*var(--teal-200);*/ /*#b2e1d8*/
  --e4s-button--ia-secondary__hover-background: var(--teal-500);
  --e4s-button--ia-secondary__active-background: var(--teal-200);
  --e4s-button--ia-secondary__hover-border-color: var(--teal-600);
  --e4s-button--ia-secondary__active-border-color: var(--teal-600);
  --e4s-button--ia-secondary__text-color: var(--slate-800);
  --e4s-button--ia-secondary__disabled-background: var(--grey-300);
  --e4s-button--ia-secondary__disabled-border-color: var(--grey-300);
  --e4s-button--ia-secondary__disabled-text-color: var(--grey-400);

  --e4s-button--destructive__background: var(--red-50);
  --e4s-button--destructive__border: var(--red-100);
  --e4s-button--destructive__text-color: var(--red-600);
  --e4s-button--destructive__hover-background: var(--red-400);

  --e4s-button--ia-destructive__border: 1px solid;
  --e4s-button--ia-destructive__border-color: var(--red-400);
  --e4s-button--ia-destructive__background: var(--red-300);
  --e4s-button--ia-destructive__hover-background: var(--red-400);
  --e4s-button--ia-destructive__active-background: var(--red-300);
  --e4s-button--ia-destructive__hover-border-color: var(--red-500);
  --e4s-button--ia-destructive__active-border-color: var(--red-400);
  --e4s-button--ia-destructive__text-color: var(--slate-800);
  --e4s-button--ia-destructive__disabled-background: var(--grey-300);
  --e4s-button--ia-destructive__disabled-border-color: var(--grey-300);
  --e4s-button--ia-destructive__disabled-text-color: var(--grey-400);

  /* Tab */
  --e4s-tab__radius: var(--blue-200);
  --e4s-tab__background: var(--blue-200);
  --e4s-tab__text-color: black;

  --e4s-tab__ia-background: var(--teal-100);
  --e4s-tab__ia-text-color: black;

  /* Form */
  --e4s-form--action-container__padding: 16px;
  --e4s-form--action-container__background: var(--slate-100);
  --e4s-form--action-container__border-radius: 0 0 7px 7px;

  /* Scrollbar */
  --e4s-scrollbar__width: 12px;
  --e4s-scrollbar__height: 12px;
  --e4s-scrollbar-track__background: transparent;
  --e4s-scrollbar-thumb__border: none;
  --e4s-scrollbar-thumb__border-radius: 50px;
  --e4s-scrollbar-thumb__background: var(--grey-500);

  /* Pagination */
  --e4s-pagination--container__padding: 12px;

  --e4s-width-controller__max-width: 1600px;
  --e4s-width-controller__max-width-mobile: 768px;
  --e4s-width-controller__repeat-grid-width: 1064px;
  --e4s-width-controller__very-wide-width: 1250px;
  --e4s-width-controller__entry-width: 1100px;
  --e4s-width-controller__width: 1100px;/*770px;*/

  /* Utilities */
  --SmallShadow: 0 2px 4px hsla(0, 0%, 0%, 0.05), 0 3px 6px hsla(0, 0%, 0%, 0.1);
  --MediumShadow: 0 4px 8px hsla(0, 0%, 0%, 0.05),
    0 6px 12px hsla(0, 0%, 0%, 0.1);
  --LargeShadow: 0 8px 16px hsla(0, 0%, 0%, 0.05),
    0 12px 24px hsla(0, 0%, 0%, 0.1);

  /* gaps */
  --e4s-gap--tiny: 2px;
  --e4s-gap--small: 4px;
  --e4s-gap--standard: 8px;
  --e4s-gap--large: 16px;
  --e4s-gap--xlarge: 32px;

  /* e4s-admin */
  --e4s-admin--background: var(--lime-300);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Cabin", sans-serif;
}

* {
  scrollbar-width: auto;
  scrollbar-color: red;
}

/* Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: var(--e4s-scrollbar__width);
  height: var(--e4s-scrollbar__height);
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: var(--e4s-scrollbar-thumb__background);
  border-radius: var(--e4s-scrollbar-thumb__border-radius);
  border: var(--e4s-scrollbar-thumb__border);
}

hr {
  margin: 20px;
  border-top: 1px solid;
  border-bottom: 0;
  border-left: 0;
  border-right: 0;
  border-color: var(--e4s-card--primary__border-color);
}

/*************************************************************************************************************************************
Reusable Components (naming convention: component-type--subsection/hierarchy__descriptor
*************************************************************************************************************************************/

.e4s-width--150 {
  width: 150px;
}

.e4s-gap--none {
  gap: 0;
}

.e4s-gap--tiny {
  gap: var(--e4s-gap--tiny);
}

.e4s-gap--small {
  gap: var(--e4s-gap--small);
}

.e4s-gap--standard {
  gap: var(--e4s-gap--standard);
}

.e4s-gap--large {
  gap: var(--e4s-gap--large);
}

.e4s-gap--x-large {
  gap: var(--e4s-gap--xlarge);
}

.e4s-header--100 {
  font: var(--e4s-header--100);
}

.e4s-header--200 {
  font: var(--e4s-header--200);
}

.e4s-header--300 {
  font: var(--e4s-header--300);
}

.e4s-header--400 {
  font: var(--e4s-header--400);
}

.e4s-header--500 {
  font: var(--e4s-header--500);
}

.e4s-subheader--general {
  color: var(--slate-500);
}

.e4s-subheader--100 {
  font: var(--e4s-subheader--100);
}

.e4s-subheader--200 {
  font: var(--e4s-subheader--200);
}

.e4s-subheader--300 {
  font: var(--e4s-subheader--300);
}

.e4s-subheader--400 {
  font: var(--e4s-subheader--400);
}

.e4s-header-subheader--container {
  gap: 4px;
  padding: var(--e4s-header-subheader--padding);
}

.e4s-body--100 {
  font: var(--e4s-body--100);
}

.e4s-body--200 {
  font: var(--e4s-body--200);
}

.e4s-body--300 {
  font: var(--e4s-body--300);
}

.e4s-body--error {
  color: var(--e4s-body--error);
}

.e4s-hyperlink--100 {
  font: var(--e4s-hyperlink--100);
  text-decoration-color: transparent;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
  transition: all 0.36s;
  outline: none;
  text-decoration: underline;
}

.e4s-hyperlink--100:hover,
.e4s-hyperlink--100:focus {
  text-decoration: underline;
  text-decoration-thickness: 2px;
}

.e4s-hyperlink--primary {
  color: var(--e4s-hyperlink--primary__text-color);
}

.e4s-hyperlink--primary:active {
  color: var(--e4s-hyperlink--primary__active-text-color);
}

.e4s-hyperlink--ia-primary {
  color: var(--e4s-hyperlink--ia-primary__text-color);
}

.e4s-hyperlink--ia-primary:hover {
  /*text-decoration-color: var(--orange-600);*/
}

.e4s-hyperlink--ia-primary:active {
  color: var(--e4s-hyperlink--ia-primary__active-text-color);
}

.e4s-hyperlink--400 {
  font: var(--e4s-header--400);
}

.e4s-badge {
  padding: var(--e4s-badge__padding);
  border-radius: var(--e4s-badge__border-radius);
}

.e4s-padding--standard {
  padding: var(--e4s-gap--standard);
}

.e4s-card {
  display: flex;
  background: var(--e4s-card--primary__background);
  border: var(--e4s-card--primary__border);
  border-color: var(--e4s-card--primary__border-color);
  border-radius: var(--e4s-card--primary__border-radius);
}

.e4s-card--generic {
  flex-direction: column;
  width: 100%;
  padding: var(--e4s-card--primary__padding);
  background: var(--e4s-card--primary__background);
}

.e4s-card--error {
  border-width: 2px;
  border-color: var(--red-600);
  background: var(--red-50);
}

.e4s-card--half-screen--DELETE {
  /*flex-direction: column;*/
  min-width: 280px;
  max-width: 770px;
  /*width: 100%;*/
  /*height: 100%;*/
  /*padding: var(--e4s-card--primary__padding);*/
  /*background: var(--e4s-card--primary__background);*/
}

.e4s-card--well {
  background: var(--stone-50);
}

.e4s-card--transparent {
  background: transparent;
  border: none;
  padding: 0;
}

.e4s-card-thin {
  padding: var(--e4s-card-thin--primary__padding)
    var(--e4s-card-superthin__padding);
}

.athlete-schedule-grid-row {
  border-width: 2px;
}

.athlete-schedule-grid-row--in-cart {
  /*background: var(--yellow-50);*/
  /*border-color: var(--yellow-400);*/
  /*border: 2px solid var(--yellow-400);*/
  background: var(--e4s-status-pill--error__background);
}

.athlete-schedule-grid-row--paid {
  /*background: var(--green-50);*/
  /*border-color: var(--green-400);*/
  /*border: 2px solid var(--blue-400);*/
  background: var(--e4s-status-pill--success__background);
}

.e4s-card--bold-neutral {
  background: var(--neutral-0);
  border-color: var(--zinc-400);
  border-width: 2px;
}

.time-entry--select {
  appearance: auto;
  width: 60px;
}

.time-entry--not-real-time {
  border: 2px solid #f44336 !important;
}

.e4s-input--container {
  margin: 4px 0;
}

.e4s-input--container > label {
  color: var(--slate-800);
}

.e4s-input--container > .e4s-body--error {
  font-weight: 700;
}

.e4s-field-label--required {
}

.e4s-field-label--optional {
}

.e4s-input--label {
  font: var(--e4s-body--100);
  color: black;
  font-size: 16px;
}

.e4s-input--label-required {
  /*color: green;*/
}

/* Style for required indicator */
.e4s-input--label-required::after {
  content: " Required"; /* Add an asterisk */
  color: red; /* Make it red for visibility */
  font-weight: bold; /* Emphasize the asterisk */
}

.e4s-input--label-required--is-valid::after {
  content: ""; /* Add an asterisk */
  color: darkgreen; /* Make it red for visibility */
  font-weight: bold; /* Emphasize the asterisk */
}

.e4s-input-field--optional {
  /*background: #0bbf0b !important;*/
}

.e4s-input--digit-number-small {
  width: 60px;
}

.e4s-input--digit-number-medium {
  width: 120px;
}

.e4s-read--label {
  color: var(--slate-500);
}

.e4s-label--black {
  color: black;
}

.e4s-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*TODO Dale  why margin???????? Use e.g. dat-e4s-hr-thick*/
.dat-e4s-hr {
  margin: 16px 0;
  background-color: var(--e4s-card--primary__border-color);
  color: var(--e4s-card--primary__border-color);
}

.dat-e4s-hr--slightly-lighter {
  border-color: var(--slate-300);
}

.dat-e4s-hr-small {
  margin: 4px 0;
}

.dat-e4s-hr-only {
  margin: 0;
}

.dat-e4s-hr-base {
  background-color: var(--slate-300);
  height: 1px;
  border: none;
  margin: 0;
}

.dat-e4s-hr-thick {
  height: 5px;
  background-color: var(--e4s-navigation-bar--primary__background);
}

.e4s-card--thick-ia {
  background-color: var(--e4s-navigation-bar--ia-primary__background);
}

.dat-e4s-hr--darker {
  background-color: var(--e4s-card--primary__border-color);
}

/*.e4s-card--thick-border {*/
/*  background-color: var(--e4s-navigation-bar--primary__background)*/
/*}*/

.e4s-vert-line {
  border-left: 1px solid var(--slate-300);
  width: 1px;
  height: 100%;
}

.e4s-info-text--success {
  color: var(--e4s-info-section--success__text-color);
}

.dat-info--success {
  background: var(--e4s-info-section--success__background);
  color: var(--e4s-info-section--success__text-color);
}

.e4s-info-text--info {
  color: var(--e4s-info-section--info__text-color);
}

.dat-info--info {
  background: var(--e4s-info-section--info__background);
  color: var(--e4s-info-section--info__text-color);
  border-color: var(--e4s-info-section--info__border-color);
}

.e4s-info-text--warn {
  color: var(--e4s-info-section--warn__text-color);
}

.dat-info--warn {
  background: var(--e4s-info-section--warn__background);
  color: var(--e4s-info-section--warn__text-color);
  border-color: var(--e4s-info-section--warn__border-color);
}

.e4s-info-text--error {
  color: var(--e4s-info-section--error__text-color);
}

/*color: var(--e4s-info-section--error__text-color);*/
.dat-info--error {
  background: var(--e4s-info-section--error__background);
  color: #5f0303;
  border-color: var(--e4s-info-section--error__border-color);
  border-width: 2px;
}

.dat-info--neutral {
  background: var(--grey-50);
  border: none;
  padding: var(--e4s-gap--standard);
  /*color: var(--e4s-info-section--error__text-color);*/
  /*border-color: var(--e4s-info-section--error__border-color);*/
}

.dat-info--pad-small {
  padding: var(--e4s-gap--small) var(--e4s-gap--standard);
}

.e4s-vertical-spacer {
  margin-top: var(--e4s-gap--large);
}

.e4s-vertical-spacer-standard {
  margin-top: var(--e4s-gap--standard);
}

.e4s-vertical-spacer-small {
  margin-top: 4px;
}

.e4s-vertical-spacer--standard {
  margin-top: var(--e4s-gap--standard);
}

.e4s-vertical-spacer--large {
  margin-top: var(--e4s-gap--large);
}

.e4s-vertical-spacer--x-large {
  margin-top: var(--e4s-gap--xlarge);
}

.dat-e4s-input--info-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.dat-e4s-input--info-label > .dat-e4s-input--info-icon-container {
  height: 12px;
  width: 12px;
  cursor: pointer;
}

.dat-e4s-input--info-icon-container > svg {
  fill: var(--blue-500);
  fill-rule: evenodd;
  transition: all 0.36s;
}

.dat-e4s-input--info-icon-container:hover > svg,
.dat-e4s-input--info-icon-container:focus > svg {
  fill: var(--blue-600);
  transition: all 0.36s;
}

.e4s-input-field {
  height: var(--e4s-input-field__height);
  margin: 4px 0;
  padding: 2px 8px;
  border: var(--e4s-input-field__border);
  border-radius: var(--e4s-input-field__border-radius);
  border-color: var(--e4s-input-field__border-color);
  background: var(--e4s-input-field__background);
  font: var(--e4s-body--100);
  color: var(--e4s-input-field__text-color);
  outline: none;
  transition: all 0.36s;
  font-size: 16px !important;
}

.e4s-input-field--150 {
  width: 150px;
}

.e4s-input-field--100 {
  width: 100px;
}

.e4s-input-field--no-margin {
  margin: 0;
}

.e4s-input-field::placeholder {
  color: var(--e4s-input-field__placeholder-text-color);
}

.e4s-input-field--primary {
  caret-color: var(--e4s-input-field--primary__caret-color);
}

.e4s-input-field--error {
  border-color: var(--e4s-input-field--error__border-color);
  color: var(--e4s-input-field--error__text-color);
  caret-color: var(--e4s-input-field--error__caret-color);
}

.e4s-input-field--primary:active,
.e4s-input-field--primary:focus {
  border-color: var(--e4s-input-field--primary__active-border-color);
  box-shadow: inset 0 0 0 1px
    var(--e4s-input-field--primary__active-border-color);
}

.e4s-input-field--primary-always-active-focus {
  border-color: var(--e4s-input-field--primary__active-border-color);
  box-shadow: inset 0 0 0 1px
    var(--e4s-input-field--primary__active-border-color);
}

/*color: var(--e4s-input-field__text-color-disabled);*/
.e4s-input-field--disabled {
  color: var(--e4s-input-field__text-color);
  background: var(--e4s-input-field__background-disabled);
  cursor: not-allowed;
}

.e4s-input-field:disabled {
  color: var(--e4s-input-field__text-color);
  background: var(--e4s-input-field__background-disabled);
}

.e4s-input-field--ia-primary {
  caret-color: var(--e4s-input-field--ia-primary__caret-color);
}

.e4s-input-field--ia-primary:active,
.e4s-input-field--ia-primary:focus {
  border-color: var(--e4s-input-field--ia-primary__active-border-color);
  box-shadow: inset 0 0 0 1px
    var(--e4s-input-field--ia-primary__active-border-color);
}

.e4s-read-field {
  height: var(--e4s-input-field__height);
  margin-top: 4px;
  font: var(--e4s-body--100);
  color: var(--e4s-input-field__text-color);
  outline: none;
  transition: all 0.36s;
  font-size: 16px !important;
}

.e4s-checkbox--large {
  height: 20px;
  width: 20px;
}

.input-checkbox-v2--label {
  display: flex;
  /*justify-content: center;*/
  align-items: center;
  vertical-align: middle;
  word-wrap: break-word;
  color: black;
  font-size: 1em;
  gap: var(--e4s-gap--standard);
}

.input-checkbox-v2--label-disabled {
  color: var(--slate-500);
  cursor: not-allowed;
}

.input-checkbox-v2--input {
  width: 16px !important;
}

.input-checkbox-v2--input:disabled {
  cursor: not-allowed;
}

.input-checkbox-v2--input:not(:checked),
.input-checkbox-v2--input:checked {
  position: unset !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

.e4s-radio span {
  color: #0f0f0f;
}

/*to counteract materialize.min*/
.e4s-radio--span {
  padding-left: 25px !important;
}

.e4s-input-field--dirty {
  background: var(--e4s-info-section--warn__border-color);
}

.e4s-input-field--required {
  /*border: 2px solid red;*/
}

[class*="e4s-button"] {
  height: var(--e4s-button__height);
  padding: var(--e4s-button__padding);
  border: var(--e4s-button__border);
  border-radius: var(--e4s-button__border-radius);
  cursor: pointer;
  transition: all 0.36s;
}

.e4s-button--60 {
  width: 60px !important;
  /*min-width: 75px;*/
}

.e4s-button--75 {
  width: 75px;
  /*min-width: 75px;*/
}

.e4s-button--100 {
  width: 100px;
  /*min-width: 100px;*/
}

.e4s-button--150 {
  width: 150px;
  /*min-width: 150px;*/
}

.e4s-button--auto {
  width: auto;
}

.e4s-button--slim {
  height: 25px;
  padding: 0;
}

.e4s-button--process {
  border-width: 2px !important;
}

.e4s-button--primary {
  background: var(--e4s-button--primary__background);
}

.e4s-button--primary:hover,
.e4s-button--primary:focus {
  background: var(--e4s-button--primary__hover-background);
}

.e4s-button--primary:active {
  background: var(--e4s-button--primary__active-background);
}

.e4s-button--primary > span {
  color: var(--e4s-button--primary__text-color);
}

/*.e4s-button--primary--text {*/
/*  color: var(--e4s-button--primary__text-color);*/
/*}*/

.e4s-button--primary:disabled,
.e4s-button--primary:disabled:hover,
.e4s-button--primary:disabled:focus {
  background: var(--e4s-button--primary__disabled-background);
  cursor: default;
}

.e4s-button--primary:disabled > span,
.e4s-button--primary:disabled:hover > span,
.e4s-button--primary:disabled:focus > span {
  color: var(--e4s-button--primary__disabled-text-color);
}

/*<secondary>*/

.e4s-button--secondary {
  border: var(--e4s-button--secondary__border);
  border-color: var(--e4s-button--secondary__border-color);
  background: var(--e4s-button--secondary__background);
}

.e4s-button--secondary:hover,
.e4s-button--secondary:focus {
  background: var(--e4s-button--secondary__hover-background);
  border-color: var(--e4s-button--secondary__hover-border-color);
}

.e4s-button--secondary:active {
  border-color: var(--e4s-button--secondary__active-border-color);
  background: var(--e4s-button--secondary__active-background);
}

.e4s-button--secondary:disabled,
.e4s-button--secondary:disabled:hover,
.e4s-button--secondary:disabled:focus {
  background: var(--e4s-button--secondary__disabled-background);
  border-color: var(--e4s-button--secondary__disabled-border-color);
  cursor: default;
}

.e4s-button--secondary:disabled > span,
.e4s-button--secondary:disabled:hover > span,
.e4s-button--secondary:disabled:focus > span {
  color: var(--e4s-button--secondary__disabled-text-color);
  border-color: var(--e4s-button--secondary__disabled-border-color);
}
/*</secondary>*/

.e4s-button--tertiary {
  border: var(--e4s-button--tertiary__border);
  border-color: var(--e4s-button--tertiary__border-color);
  background: var(--e4s-button--tertiary__background);
}

.e4s-button--tertiary:hover,
.e4s-button--tertiary:focus {
  background: var(--e4s-button--tertiary__hover-background);
  border-color: var(--e4s-button--tertiary__hover-border-color);
}

.e4s-button--tertiary:active {
  border-color: var(--e4s-button--tertiary__active-border-color);
  background: var(--e4s-button--tertiary__active-background);
}

.e4s-button--tertiary:disabled,
.e4s-button--tertiary:disabled:hover,
.e4s-button--tertiary:disabled:focus {
  background: var(--e4s-button--tertiary__disabled-background);
  cursor: default;
}

.e4s-button--tertiary:disabled > span,
.e4s-button--tertiary:disabled:hover > span,
.e4s-button--tertiary:disabled:focus > span {
  color: var(--e4s-button--tertiary__disabled-text-color);
}

.e4s-button--ia-primary {
  background: var(--e4s-button--ia-primary__background);
}

.e4s-button--ia-primary:hover,
.e4s-button--ia-primary:focus {
  background: var(--e4s-button--ia-primary__hover-background);
}

.e4s-button--ia-primary:active {
  background: var(--e4s-button--ia-primary__active-background);
}

.e4s-button--ia-primary > span {
  color: var(--e4s-button--ia-primary__text-color);
}

.e4s-button--ia-primary:disabled,
.e4s-button--ia-primary:disabled:hover,
.e4s-button--ia-primary:disabled:focus {
  background: var(--e4s-button--tertiary__disabled-background);
  cursor: default;
}

.e4s-button--ia-primary:disabled > span,
.e4s-button--ia-primary:disabled:hover > span,
.e4s-button--ia-primary:disabled:focus > span {
  color: var(--e4s-button--tertiary__disabled-text-color);
}

/*<ia secondary>*/

.e4s-button--ia-secondary {
  border: var(--e4s-button--ia-secondary__border);
  border-color: var(--e4s-button--ia-secondary__border-color);
  background: var(--e4s-button--ia-secondary__background);
}

.e4s-button--ia-secondary:hover,
.e4s-button--ia-secondary:focus {
  background: var(--e4s-button--ia-secondary__hover-background);
  border-color: var(--e4s-button--ia-secondary__hover-border-color);
}

.e4s-button--ia-secondary:active {
  border-color: var(--e4s-button--ia-secondary__active-border-color);
  background: var(--e4s-button--ia-secondary__active-background);
}

.e4s-button--ia-secondary:disabled,
.e4s-button--ia-secondary:disabled:hover,
.e4s-button--ia-secondary:disabled:focus {
  background: var(--e4s-button--ia-secondary__disabled-background);
  border-color: var(--e4s-button--ia-secondary__disabled-border-color);
  cursor: default;
}

.e4s-button--ia-secondary:disabled > span,
.e4s-button--ia-secondary:disabled:hover > span,
.e4s-button--ia-secondary:disabled:focus > span {
  color: var(--e4s-button--ia-secondary__disabled-text-color);
  border-color: var(--e4s-button--ia-secondary__disabled-border-color);
}
/*</ia secondary>*/

.e4s-button--destructive {
  background: var(--e4s-button--destructive__background);
  border: 1px solid var(--e4s-button--destructive__border);
}

.e4s-button--destructive-x {
  width: 35px;
}

.e4s-button--admin {
  border: 2px solid var(--e4s-input-field--error__text-color);
}

/*.e4s-button--destructive-2 {*/
/*  border: 2px solid var(--e4s-button--destructive__border);*/
/*  border-left: 1px solid var(--e4s-button--destructive__border);*/
/*}*/

/*.e4s-button--secondary-2 {*/
/*  border: 2px solid var(--e4s-button--secondary__border-color);*/
/*  border-left: 1px solid var(--e4s-button--secondary__border-color);*/
/*}*/

.e4s-button--destructive > span {
  color: var(--e4s-button--destructive__text-color);
}

.e4s-button--destructive:hover {
  background: var(--e4s-button--destructive__hover-background);
}

.e4s-button--ia-destructive {
  background: var(--e4s-button--ia-destructive__background);
  border: 1px solid var(--e4s-button--ia-destructive__border-color);
}

.e4s-button--ia-destructive > span {
  color: var(--e4s-button--ia-destructive__text-color);
}

.e4s-button--ia-destructive:hover {
  background: var(--e4s-button--ia-destructive__hover-background);
}

.e4s-sticky-save--container {
  position: sticky;
  bottom: 0;
  background: var(--slate-400);
  padding: var(--e4s-gap--standard);
  border-top: 2px solid var(--e4s-card--primary__border-color);
}

.e4s-sticky-save--container-ia {
  background: var(--secondary-color);
}

.e4s-status-pill {
  width: fit-content;
  padding: var(--e4s-status-pill__padding);
  /*border: none;*/
  border-radius: var(--e4s-status-pill__border-radius);
}

.e4s-status-pill--success {
  background: var(--e4s-status-pill--success__background);
  color: var(--e4s-status-pill--success__text-color);
  border: 1px solid var(--lime-300);
}

.e4s-status-pill--alert {
  background: var(--e4s-status-pill--alert__background);
  color: var(--e4s-status-pill--alert__text-color);
}

.e4s-status-pill--error {
  background: var(--e4s-status-pill--error__background);
  color: var(--e4s-status-pill--success__text-color);
  border: 1px solid var(--red-200);
}

.e4s-pill {
  font: var(--e4s-subheader--400);
  text-transform: uppercase;
}

.e4s-pill--content {
}

.e4s-pill-75 {
  width: 75px;
}

.e4s-pill-100 {
  width: 100px;
}

.e4s-pill-150 {
  width: 150px;
}

.e4s-pill--success {
  background: var(--e4s-status-pill--success__background);
  border: 1px solid var(--e4s-status-pill--success__border-color);
}
.e4s-pill--success > span {
  color: var(--e4s-status-pill--success__text-color);
}

.e4s-pill--ia-success {
  background: var(--e4s-status-pill--alert__background);
}
.e4s-pill--ia-success > span {
  color: var(--e4s-status-pill--alert__text-color);
}

.e4s-pill--error {
  background: var(--e4s-status-pill--error__background);
}
.e4s-pill--error > span {
  color: var(--e4s-status-pill--error__text-color);
}

.e4s-status-pill--open-competition {
  background: var(--e4s-status-pill--success__background);
}
.e4s-status-pill--open-competition > span {
  color: var(--e4s-status-pill--success__text-color);
}

.e4s-status-pill--ia-open-competition {
  background: var(--e4s-status-pill--alert__background);
}
.e4s-status-pill--ia-open-competition > span {
  color: var(--e4s-status-pill--alert__text-color);
}

.e4s-status-pill--closed-competition {
  background: var(--e4s-status-pill--error__background);
}
.e4s-status-pill--closed-competition > span {
  color: var(--e4s-status-pill--error__text-color);
}

/*************************************************************************************************************************************
Utilities
*************************************************************************************************************************************/

.e4s-app-wrapper {
  position: relative;
  min-height: 100vh;
  background-color: var(--e4s-body--backgroundColor);
}

.e4s-body-wrapper {
  display: grid;
  width: 100%;
  margin: 0 auto;
  grid-template-rows: auto auto auto;
  grid-template-columns: minmax(280px, 1fr);
  background-color: var(--e4s-body--backgroundColor);
}

.e4s-body-wrapper > .e4s-sticky-navigation {
  z-index: 100;
}

.e4s-body-wrapper--router-content {
  /*padding-bottom: calc(20px + var(--e4s-footer__height));*/
  padding: 0 var(--e4s-gap--standard) calc(20px + var(--e4s-footer__height))
    var(--e4s-gap--standard);
}

.e4s-content-wrapper {
  max-width: var(--e4s-width-controller__max-width) px;
  width: 100%;
  /*Was 40px*/
  margin: 20px auto;
  gap: 20px;
}

.e4s-content-max-center {
  max-width: var(--e4s-width-controller__max-width) px;
  margin: 0 auto;
}

.e4s-flex {
  display: flex;
}

.e4s-flex-column {
  display: flex;
  flex-direction: column;
}

.e4s-flex-row {
  display: flex;
  flex-direction: row;
}

.e4s-flex-wrap {
  flex-wrap: wrap;
}

.e4s-flex-nowrap {
  flex-wrap: nowrap;
}

.e4s-flex-grow {
  flex-grow: 1;
}

.e4s-flex-start {
  align-items: flex-start;
}

.e4s-flex-center {
  align-items: center;
}

.e4s-flex-end {
  align-items: flex-end;
}

/*Use if you usually space evenly left right with "justify", but left bit is hidden*/
.e4s-flex-row--end {
  margin-left: auto;
}

.e4s-align-self-flex-start {
  align-self: flex-start;
}

.e4s-align-self-flex-end {
  align-self: flex-end;
}

.e4s-align-self-flex-center {
  align-self: center;
}

.e4s-justify-flex-start {
  justify-content: flex-start;
}

/*row*/
.e4s-justify-flex-center {
  justify-content: center;
}

.e4s-justify-flex-end {
  justify-content: flex-end;
}

.e4s-justify-flex-space-between {
  justify-content: space-between;
}

.e4s-justify-flex-space-around {
  justify-content: space-around;
}

.e4s-justify-flex-space-evenly {
  justify-content: space-evenly;
}

.e4s-equal-width-spans * {
  flex: 1;
  text-align: center;
}

.e4s-justify-flex-row-vert-center {
  align-items: center;
}

.e4s-justify-flex-row-vert-top {
  align-items: flex-start;
}

.e4s-full-width {
  width: 100%;
}

.e4s-full-height {
  height: 100%;
}

.e4s-square--right {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  /*border-right: 0;*/
}

.e4s-square-top {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top: 0;
}

.e4s-square-bottom {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: 0;
}

.e4s-square--right-no-border {
  border-right: 0;
}

.e4s-square--button {
  width: 35px;
}
.e4s-square--left {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}

.e4s-square--left-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.e4s-sticky-navigation {
  position: sticky;
  top: 0;
  width: 100%;
}

.e4s-repeatable-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 500px));
  gap: 20px;
  justify-content: space-between;
}

.e4s-repeatable-grid--header {
  background-color: var(--slate-100);
}

.e4s-repeatable-grid--top {
  border-top: 1px solid var(--slate-200);
}

.e4s-repeatable-grid--top-match-card {
  border-top: 1px solid var(--slate-400);
}

.e4s-repeatable-grid--bottom-match-card {
  border-bottom: 1px solid var(--slate-400);
}

.e4s-two-column-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.e4s-50-percent-width {
  flex: 0 0 50%;
}

.e4s-50-percent-width:nth-child(even) {
  align-items: flex-end;
}

.e4s-grid-3-columns {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

@media screen and (max-width: 1100px) {
  .e4s-repeatable-grid {
    justify-content: center;
  }
}

/*<Make a row, unless mobile then column>*/
.e4s-container--row-mobile-col {
  flex-direction: row;
}

@media screen and (max-width: 768px) {
  .e4s-container--row-mobile-col {
    flex-direction: column;
  }

  .e4s-mobile-only-padding {
    padding: 8px;
  }
}
/*</Make a row, unless mobile then column>*/

/*@media screen and (max-width: 768px) {*/
/*    .e4s-repeatable-grid {*/
/*        justify-content: center;*/
/*    }*/
/*}*/

.e4s-scroll-box {
  overflow: auto;
  visibility: hidden;
  transition: visibility 0.26s;
}

.e4s-scroll-box div,
.e4s-scroll-box:hover,
.e4s-scroll-box:focus {
  visibility: visible;
}

.e4s-accordion--header {
  cursor: pointer;
}

.e4s-accordion--button {
  font-size: 35px;
  color: red;
}

/*.e4s-width-controller-entry {*/
/*    width: var(--e4s-width-controller__entry-width);*/
/*}*/

/*.e4s-width-controller {*/
/*    width: var(--e4s-width-controller__width);*/
/*}*/

/*************************************************************************************************************************************
<form>>
*************************************************************************************************************************************/
.dat-e4s-card--form {
  position: relative;
  min-width: 280px;
  max-width: var(--e4s-width-controller__max-width) px;
  width: 100%;
  margin: 0 auto;
  background: var(--e4s-card--primary__background);
}

.dat-e4s-card--form-fixed {
  max-height: calc(100vh - (var(--e4s-navigation-bar__height) + 80px));
  overflow-y: hidden;
}

.dat-e4s-card--form-fixed > form {
  overflow-y: overlay;
}

.dat-e4s-card--form-fixed > form > .dat-e4s-form--action-container {
  position: sticky;
  bottom: 0;
}

.dat-e4s-card--form > section:first-of-type {
  border-radius: 7px 7px 0 0;
  background: var(--neutral-0);
}

.dat-e4s-card--form > form > section:first-of-type {
  padding-top: 0;
}

.dat-e4s-form--control-bar-container {
  gap: 16px;
}

.dat-e4s-form--content-container {
  padding: var(--e4s-card--primary__padding);
}

.dat-e4s-form--control-bar-icon-container {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.dat-e4s-form--close-icon-container > svg > path,
.dat-e4s-form--expand-icon-container > svg > path {
  fill: var(--grey-400);
  transition: all 0.36s;
}

.dat-e4s-form--close-icon-container > svg > path {
  fill-rule: evenodd;
}

.dat-e4s-form--expand-icon-container:hover > svg > path {
  fill: var(--grey-600);
}

.dat-e4s-form--close-icon-container:hover > svg > path {
  fill: var(--red-700);
}

.dat-e4s-form--title-container {
  margin: 16px 0;
  gap: 8px;
}

.dat-e4s-form--title-container > h1,
.date-e4s-form--title-container > h2 {
  color: var(--slate-800);
}

.dat-e4s-form--title-container > p {
  color: var(--slate-500);
}

/********/
/*<TabsNew>*/
/********/
.e4s-tab-links--section-links-wrapper {
  width: 100%;
  border-bottom: 1px solid var(--e4s-card--primary__border-color);
}

.e4s-tab-links--section-links-bottom {
  border-bottom: 1px solid var(--e4s-card--primary__border-color);
}

.e4s-tab-links--main {
}

@media screen and (max-width: 768px) {
  .e4s-tab-links--main button span {
    font-size: 16px;
  }
}

.e4s-tab-links--sub {
  /*background-color: white;*/
  margin-left: var(--e4s-gap--standard);
}

.e4s-tab-links--sub button span {
  font-size: 16px;
}

.e4s-tab-links--section-links-container {
  gap: var(--e4s-gap--standard);
  flex-wrap: nowrap;
  overflow-x: auto;
}

@media screen and (max-width: 768px) {
  .e4s-tab-links--section-links-container {
    flex-wrap: wrap;
  }
}

.e4s-tab-link--default {
  border-left: none;
  border-top: none;
  border-right: none;
  border-bottom: 4px solid transparent;
  background: transparent;
  height: 30px;
  /*min-width: 100px;*/
  padding: 0 8px;
  color: black;
}

.e4s-tab-link--builder {
  height: auto;
  width: 200px;
}

.e4s-tab-link--builder-button-text {
  font-size: 25px;
}

@media screen and (max-width: 768px) {
  .e4s-tab-link--default {
    /*min-width: 80px;*/
  }
}

/*.e4s-tab-link--100 {*/
/*  min-width: 100px;*/
/*}*/

.e4s-tab-link--default:focus {
  background: transparent;
}

@media (hover: hover) {
  .e4s-tab-link--default:hover {
    border-bottom: 4px solid var(--slate-400);
  }
}

.e4s-tab-link--selected {
  color: var(--e4s-form-nav-link--primary__text-color);
  border-bottom: 4px solid var(--e4s-navigation-bar--primary__background);
}

.e4s-tab-link--ia-selected {
  color: var(--e4s-form-nav-link--ia-primary__text-color);
  border-bottom: 4px solid var(--e4s-navigation-bar--ia-primary__background);
}

.e4s-tab-link--disabled {
  border-bottom: 4px solid var(--slate-400);
}

.e4s-tab-link--disabled span {
  color: var(--slate-400);
}

/********/
/*</TabsNew>*/
/********/

/********/
/*<Tabs>*/
/********/
.dat-e4s-form--section-links-wrapper {
  width: 100%;
  border-bottom: 1px solid var(--e4s-card--primary__border-color);
}

.dat-e4s-form--section-links-container {
  padding: 16px 0 0 0;
}

.dat-e4s-form--section-links-container > div {
  gap: var(--e4s-gap--standard);
  padding: 0 0 8px 0;
  flex-wrap: nowrap;
}

[class*="dat-e4s-form-section-link"] {
  text-decoration: none;
  white-space: nowrap;
  gap: 4px;
  transition: all 0.36s;
}

[class*="dat-e4s-form-section-link"] > div {
  height: var(--e4s-form-nav-link-indicator__height);
  width: var(--e4s-form-nav-link-indicator__width);
  border-radius: var(--e4s-form-nav-link-indicator__border-radius);
  background: var(--e4s-form-nav-link-indicator__background);
}

[class*="dat-e4s-form-section-link"] > div > span {
  font-weight: 700;
  color: var(--e4s-form-nav-link-indicator__text-color);
}

[class*="dat-e4s-form-section-link"] > span {
  color: var(--e4s-form-nav-link__text-color);
}

.dat-e4s-form-section-link--primary,
.dat-e4s-form-section-link--ia-primary {
  width: 18px;
  transition: all 0.36s;
  border: 4px solid transparent;
}

.dat-e4s-form-section-link--selected {
  border-bottom: 4px solid var(--e4s-navigation-bar--primary__background);
}

.dat-e4s-form-section-link--primary.dat-e4s-form-section-link--selected > div {
  background: var(--e4s-form-nav-link-indicator--primary__background);
}

.dat-e4s-form-section-link--primary.dat-e4s-form-section-link--selected
  > div
  > span {
  color: var(--e4s-form-nav-link-indicator--primary__text-color);
}

.dat-e4s-form-section-link--primary.dat-e4s-form-section-link--selected > span {
  color: var(--e4s-form-nav-link--primary__text-color);
}

@media (hover: hover) {
  .dat-e4s-form-section-link--primary:hover {
    border-bottom: 4px solid pink;
  }

  .dat-e4s-form-section-link--primary:hover > span {
    color: var(--e4s-form-nav-link--primary__text-color);
    transition: all 0.36s;
  }
}

.dat-e4s-form-section-link--ia-primary.dat-e4s-form-section-link--selected
  > div {
  background: var(--e4s-form-nav-link-indicator--ia-primary__background);
}

.dat-e4s-form-section-link--ia-primary.dat-e4s-form-section-link--selected
  > div
  > span {
  color: var(--e4s-form-nav-link-indicator--ia-primary__text-color);
}

.dat-e4s-form-section-link--ia-primary.dat-e4s-form-section-link--selected
  > span {
  color: var(--e4s-form-nav-link--ia-primary__text-color);
}

.dat-e4s-form-section-link--disabled {
  color: var(--e4s-button--primary__disabled-text-color);
}

.dat-e4s-form-section-link--standard {
  width: 100px;
}

.dat-e4s-form-section-link--secondary {
  font-weight: normal;
}

.dat-e4s-form-section-link--auto {
  width: auto;
}

/********/
/*</Tabs>*/
/********/

/********/
/*</Tabs-v1>*/
/********/

.e4s-tabs-v1 {
}

div .e4s-tabs-v1 .tab {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border: 1px solid var(--e4s-navigation-bar--primary__background);
}

div .e4s-tabs-v1--ia .tab {
  border: 1px solid var(--e4s-navigation-bar--ia-primary__background);
}

div .e4s-tabs-v1 .tab a.active {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background-color: var(--e4s-tab__background) !important;
  color: var(--e4s-tab__text-color) !important;
}

div .e4s-tabs-v1--ia .tab a.active {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background-color: var(--e4s-tab__ia-background) !important;
  color: var(--e4s-tab__ia-text-color) !important;
}

/********/
/*</Tabs-v1>*/
/********/

.dat-e4s-form-section--title-container {
  margin: 28px 0 20px 0;
}

.dat-e4s-form-input-control--grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  grid-gap: 16px;
  margin: 8px 0;
}

.dat-e4s-form-input-control--grid--tight {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  grid-gap: var(--e4s-gap--standard);
  margin: 2px 0;
}

.dat-e4s-form-input-control--grid-2-col {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.dat-e4s-form--action-container {
  padding: var(--e4s-form--action-container__padding);
  background-color: var(--e4s-form--action-container__background);
  border-radius: var(--e4s-form--action-container__border-radius);
  gap: 12px;
}

.nick-grid {
  display: grid;
  gap: 1rem;
  /*grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));*/
  grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));
}

.e4s-card--competition__logo-container {
  /*width: 250px;*/
  /*max-width: 250px;*/
  /*height: 67px;*/
  /*border-radius: var(--e4s-card--primary__border-radius);*/
  /*background: var(--slate-100);*/
}

.e4s-card--competition__logo-container img {
  /*width: 100%;*/
  max-width: 250px;
  height: 50px;
}

@media screen and (max-width: 768px) {
  /*.e4s-card--competition__logo-container {*/
  /*  max-width: 150px;*/
  /*}*/
  /*.e4s-card--competition__logo-container img {*/
  /*  max-width: 100%;*/
  /*  height: auto;*/
  /*}*/
}

/*@media screen and (max-width: 768px) {*/
/*  .test:not(.dat-e4s-form-section-link--primary):hover {*/
/*    border: 1px solid red;*/
/*  }*/
/*}*/

/*************************************************************************************************************************************
</form>>
*************************************************************************************************************************************/

/*************************************************************************************************************************************
Media queries to target specific screen sizes
*************************************************************************************************************************************/

@media screen and (max-width: 1599px) {
  .e4s-content-wrapper {
    padding: 0;
  }
}

@media screen and (max-width: 768px) {
  .e4s-content-wrapper {
    margin: var(--e4s-gap--standard) auto;
  }

  /*.e4s-content-wrapper {*/
  /*  padding: 0 8px;*/
  /*}*/

  /*.e4s-width-controller-entry {*/
  /*    min-width: 280px;*/
  /*    max-width: var(--e4s-width-controller__width);*/
  /*}*/

  /*.e4s-width-controller {*/
  /*    min-width: 280px;*/
  /*    max-width: var(--e4s-width-controller__width);*/
  /*}*/
}

/*************************************************************************************************************************************
<NAV BAR>
*************************************************************************************************************************************/
.e4s-navigation-bar {
  display: flex;
  height: var(--e4s-navigation-bar__height);
}

.e4s-navigation-bar--primary {
  background: var(--e4s-navigation-bar--primary__background);
}

.e4s-navigation-bar--ia-primary {
  background: var(--e4s-navigation-bar--ia-primary__background);
}

.e4s-navigation-bar--content-wrapper {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: var(--e4s-width-controller__max-width) px;
  margin: 0 auto 0 auto;
  gap: 64px;
  padding-right: 25px;
}

.e4s-navigation-bar-logo--container {
  display: flex;
  align-items: center;
  /*justify-content: center;*/
  /*width: 120px;*/
  /*max-width: 120px;*/
  /*min-width: 120px;*/
  height: 100%;
}

.e4s-navigation-bar-logo--container svg g,
.e4s-navigation-bar-logo--container svg path {
  fill: var(--neutral-0);
}

@media screen and (max-width: 1100px) {
  .e4s-navigation-bar-logo--container {
    padding-left: var(--e4s-gap--standard);
  }
}

.e4s-navigation-bar--primary .e4s-navigation-bar-submenu {
  background: var(--e4s-navigation-bar--primary__background);
}

.e4s-navigation-bar--ia-primary .e4s-navigation-bar-submenu {
  background: var(--e4s-navigation-bar--ia-primary__background);
}

.e4s-navigation-bar-menu {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  list-style-type: none;
  gap: 10px;
}

.e4s-navigation-bar-menu--item {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 120px;
  height: 100%;
  text-align: end;
}

.e4s-navigation-bar-submenu--item {
  position: relative;
}

.e4s-navigation-bar-menu--item a > span,
.e4s-navigation-bar-submenu--item a > span {
  padding: 8px;
  border-radius: 4px;
}

.e4s-navigation-bar--primary .e4s-navigation-bar-menu--item a:hover > span {
  background: var(--e4s-navigation-link--primary__hover-background);
  transition: all 0.36s;
}

.e4s-navigation-bar--ia-primary .e4s-navigation-bar-menu--item a:hover > span {
  background: var(--e4s-navigation-link--ia-primary__hover-background);
  transition: all 0.36s;
}

.e4s-navigation-bar-menu a,
.e4s-navigation-bar a {
  text-decoration: none;
}

.e4s-navigation-bar--primary .e4s-navigation-bar-menu a,
.e4s-navigation-bar--primary .e4s-navigation-bar-submenu a {
  color: var(--e4s-navigation-link--primary__text-color);
}

.e4s-navigation-bar--ia-primary .e4s-navigation-bar-menu a,
.e4s-navigation-bar--ia-primary .e4s-navigation-bar-submenu a {
  color: var(--e4s-navigation-link--ia-primary__text-color);
}

.e4s-navigation-bar--primary .e4s-navigation-bar-menu a,
.e4s-navigation-bar--ia-primary .e4s-navigation-bar-menu a {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.e4s-navigation-bar-menu a {
  padding: 8px;
}

.e4s-navigation-bar-menu--submenu-closed
  .e4s-navigation-bar-submenu-dropdown
  span::after {
  content: "\25BE";
}

.e4s-navigation-bar-menu--submenu-open
  .e4s-navigation-bar-submenu-dropdown
  span::after {
  content: "\25B4";
}

.e4s-navigation-bar-menu--submenu-open
  .e4s-navigation-bar-submenu-dropdown
  span::after {
  content: "\25B4";
}

.e4s-navigation-bar-submenu {
  position: absolute;
  top: var(--e4s-navigation-bar__height);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 16px 0;
  border-radius: 0 0 4px 4px;
  gap: 16px;
  list-style-type: none;
}

.e4s-navigation-bar-submenu--hidden {
  display: none;
}

.e4s-badge--navigation {
  position: absolute;
  top: 5px;
  right: -10px;
  min-width: 24px;
  text-align: center;
}

.e4s-navigation-bar--primary .e4s-badge--navigation,
.e4s-mobile-nav-bar--primary .e4s-badge--navigation {
  background: var(--e4s-badge--navigation--primary__background);
}

.e4s-navigation-bar--primary .e4s-badge--navigation > span,
.e4s-mobile-nav-bar--primary .e4s-badge--navigation > span {
  color: var(--e4s-badge--navigation--primary__text-color);
}

.e4s-navigation-bar--ia-primary .e4s-badge--navigation,
.e4s-mobile-nav-bar--ia-primary .e4s-badge--navigation {
  background: var(--e4s-badge--navigation--ia-primary__background);
}

.e4s-navigation-bar--ia-primary .e4s-badge--navigation > span,
.e4s-mobile-nav-bar--ia-primary .e4s-badge--navigation > span {
  color: var(--e4s-badge--navigation--ia-primary__text-color);
}

.e4s-navigation-bar--mobile-wrapper {
  display: none;
  gap: 32px;
}

.e4s-navigation-bar--primary
  .e4s-navigation-bar--mobile-wrapper
  .e4s-navigation-bar-menu--item
  a {
  color: var(--e4s-navigation-link--primary__text-color);
}

.e4s-navigation-bar--ia-primary
  .e4s-navigation-bar--mobile-wrapper
  .e4s-navigation-bar-menu--item
  a {
  color: var(--e4s-navigation-link--ia-primary__text-color);
}

.e4s-navigation-bar--mobile-wrapper
  .e4s-navigation-bar-menu--item
  .e4s-badge--navigation {
  top: -20px;
  right: -22px;
}

.e4s-navigation-bar-menu--icon-container {
  width: 28px;
  height: 28px;
}

.e4s-navigation-bar--primary .e4s-navigation-bar-menu--icon-container svg {
  fill: var(--e4s-navigation-link--primary__text-color);
}

.e4s-navigation-bar--ia-primary .e4s-navigation-bar-menu--icon-container svg {
  fill: var(--e4s-navigation-link--ia-primary__text-color);
}

.e4s-navigation-bar__hamburger-container {
  display: none;
}

.e4s-navigation__hamburger {
  border: none;
  background: transparent;
  cursor: pointer;
}

.e4s-navigation-bar_hamburger {
  display: none;
}

.hamburger {
  padding: 15px 15px;
  display: flex;
  cursor: pointer;
  transition-property: opacity, filter;
  transition-duration: 0.15s;
  transition-timing-function: linear;
  font: inherit;
  color: inherit;
  text-transform: none;
  background-color: transparent;
  border: 0;
  margin: 0;
  overflow: visible;
}

.hamburger:hover {
  opacity: 0.7;
}

.hamburger.is-active:hover {
  opacity: 0.7;
}

.hamburger-box {
  width: 40px;
  height: 24px;
  display: inline-block;
  position: relative;
}

.hamburger-inner {
  display: block;
  top: 50%;
  margin-top: -2px;
}

.hamburger-inner,
.hamburger-inner::before,
.hamburger-inner::after {
  width: 40px;
  height: 4px;
  border-radius: 4px;
  position: absolute;
  transition-property: transform;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}

.e4s-navigation-bar--primary .hamburger-inner,
.e4s-navigation-bar--primary .hamburger-inner::before,
.e4s-navigation-bar--primary .hamburger-inner::after,
.e4s-navigation-bar--primary .hamburger.is-active .hamburger-inner,
.e4s-navigation-bar--primary .hamburger.is-active .hamburger-inner::before,
.e4s-navigation-bar--primary .hamburger.is-active .hamburger-inner::after {
  background: var(--e4s-navigation-link--primary__text-color);
}

.e4s-navigation-bar--ia-primary .hamburger-inner,
.e4s-navigation-bar--ia-primary .hamburger-inner::before,
.e4s-navigation-bar--ia-primary .hamburger-inner::after,
.e4s-navigation-bar--ia-primary .hamburger.is-active .hamburger-inner,
.e4s-navigation-bar--ia-primary .hamburger.is-active .hamburger-inner::before,
.e4s-navigation-bar--ia-primary .hamburger.is-active .hamburger-inner::after {
  background: var(--e4s-navigation-link--ia-primary__text-color);
}

.hamburger-inner::before,
.hamburger-inner::after {
  content: "";
  display: block;
}

.hamburger-inner::before {
  top: -10px;
}

.hamburger-inner::after {
  bottom: -10px;
}

.hamburger--slider .hamburger-inner {
  top: 2px;
}

.hamburger--slider .hamburger-inner::before {
  top: 10px;
  transition-property: transform, opacity;
  transition-timing-function: ease;
  transition-duration: 0.15s;
}

.hamburger--slider .hamburger-inner::after {
  top: 20px;
}

.hamburger--slider.is-active .hamburger-inner {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--slider.is-active .hamburger-inner::before {
  transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
  opacity: 0;
}

.hamburger--slider.is-active .hamburger-inner::after {
  transform: translate3d(0, -20px, 0) rotate(-90deg);
}

.e4s-admin--section {
  /*background: var(--e4s-admin--background);*/
  border-color: var(--red-600);
}

.e4s-admin--section input {
  border-color: var(--red-600);
  border-width: 2px;
}

.e4s-admin--section label {
  color: var(--red-900);
}

.e4s-admin--section input[type="radio"],
input[type="checkbox"] {
  color: var(--red-900);
}

.e4s-admin--section .e4s-radio span {
  color: var(--red-900);
}

@media screen and (max-width: var(--e4s-width-controller__max-width)) {
  .e4s-navigation-bar--content-wrapper {
    padding: 0;
  }
}

@media screen and (max-width: 864px) {
  .e4s-navigation-bar {
    overflow: hidden;
  }

  .e4s-navigation-bar--primary .e4s-navigation-bar-menu--mobile {
    background: var(--e4s-navigation-bar--primary__background);
  }

  .e4s-navigation-bar--ia-primary .e4s-navigation-bar-menu--ia-mobile {
    background: var(--e4s-navigation-bar--ia-primary__background);
  }

  .e4s-navigation-bar--content-wrapper {
    gap: 20px;
  }

  .e4s-navigation-bar-menu {
    display: none;
  }

  .e4s-navigation-bar--mobile-wrapper {
    display: flex;
  }

  .e4s-navigation-bar-menu--mobile {
    position: absolute;
    top: var(--e4s-navigation-bar__height);
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%;
    height: 100vh;
    padding: 16px 0;
    align-items: flex-start;
    gap: 24px;
    overflow-y: scroll;
  }

  .e4s-navigation-bar-menu--mobile .e4s-navigation-bar-menu--item {
    max-width: unset;
    width: unset;
    height: unset;
    align-items: flex-start;
    text-align: left;
  }

  .e4s-navigation-bar-menu--mobile .e4s-navigation-bar-menu--has-submenu {
    display: flex;
    flex-direction: column;
  }

  .e4s-navigation-bar-menu--mobile .e4s-navigation-bar-submenu {
    position: relative;
    top: 0;
    left: 0;
    align-items: flex-start;
    padding: 8px 0;
  }

  .e4s-navigation-bar-menu--mobile .e4s-badge--navigation {
    top: 6px;
    right: -24px;
  }

  .e4s-navigation-bar__hamburger-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .e4s-navigation-bar--primary .e4s-navigation__hamburger-icon {
    fill: var(--e4s-navigation-link--primary__text-color);
  }

  .e4s-navigation-bar--ia-primary .e4s-navigation__hamburger-icon {
    fill: var(--e4s-navigation-link--ia-primary__text-color);
  }

  .e4s-navigation-bar-menu--mobile .e4s-badge--navigation {
    top: 6px;
    right: -24px;
  }

  .e4s-navigation-bar-submenu--mobile-item-span {
    padding-left: 16px;
  }
}
/*************************************************************************************************************************************
</NAV BAR>
*************************************************************************************************************************************/

.e4s-width-controller-entry {
  width: var(--e4s-width-controller__entry-width);
}

/*.e4s-width-controller-entry-v1 {*/
/*  width: var(--e4s-width-controller__entry-width);*/
/*}*/

.e4s-width-controller--builder {
  width: var(--e4s-width-controller__very-wide-width);
  max-width: var(--e4s-width-controller__very-wide-width);
  min-width: 200px;
}

.e4s-width-controller {
  max-width: var(--e4s-width-controller__width);
  width: 100%;
}

@media screen and (max-width: 768px) {
  .e4s-width-controller-entry {
    min-width: 280px;
    max-width: var(--e4s-width-controller__width);
  }

  .e4s-hybrid-content-wrapper {
  }

  /*.e4s-width-controller-entry-v1 {*/
  /*  min-width: 280px;*/
  /*  max-width: var(--e4s-width-controller__width);*/
  /*}*/

  .e4s-width-controller {
    min-width: 280px;
    max-width: 100%;
  }
  /*max-width: var(--e4s-width-controller__width);*/

  .e4s-button-mobile--60 {
    width: 60px;
  }
}

/*************************************************************************************************************************************
<FOOTER>
*************************************************************************************************************************************/
/*.e4s-footer {*/
/*    position: relative;*/
/*    bottom: 0;*/
/*    display: flex;*/
/*    justify-content: center;*/
/*    align-self: flex-end;*/
/*    width: 100%;*/
/*    height: var(--e4s-footer__height);*/
/*    margin: var(--e4s-footer__margin);*/
/*    overflow: hidden;*/
/*}*/
.e4s-footer {
  position: absolute;
  bottom: 0;
  display: flex;
  overflow: hidden;
  align-items: center;
  width: 100%;
  height: var(--e4s-footer__height);
  /*height: 60px;*/
}

.e4s-footer--primary {
  background: var(--e4s-footer--primary__background);
}

.e4s-footer--ia-primary {
  background: var(--e4s-footer--ia-primary__background);
}

.e4s-footer--wrapper {
  /*width: var(--e4s-width-controller__entry-width);*/
  max-width: 100%;
}

.e4s-footer--text {
  color: var(--e4s-footer--primary__text-color);
}

.e4s-footer--links {
}

.e4s-footer-links__wrapper {
  max-width: var(--e4s-width-controller__max-width);
  width: 100%;
  margin: 16px 0;
  gap: 40px;
}

.e4s-footer-links__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /*gap: 40px;*/
}

.e4s-footer-links__container {
}

.e4s-footer-links__container-right {
  align-items: flex-end;
}

.e4s-footer__logo-container {
  width: 156px;
  height: 52px;
  margin: 0 0 16px 0;
  background: rgba(255, 255, 255, 0.5);
}

.e4s-footer-link__container {
  gap: 4px;
}

.e4s-footer-link__container > h5 {
  /*margin: 0 0 16px 0;*/
}

.e4s-footer--primary .e4s-footer-link__container h5,
.e4s-footer--primary .e4s-footer-link__container a,
.e4s-footer--primary .e4s-footer__copyright-date {
  color: var(--e4s-footer--primary__text-color);
}

.e4s-footer--ia-primary .e4s-footer-link__container h5,
.e4s-footer--ia-primary .e4s-footer-link__container a,
.e4s-footer--ia-primary .e4s-footer__copyright-date {
  color: var(--e4s-footer--ia-primary__text-color);
}

.e4s-footer-link__icon-container {
  width: 24px;
  height: 24px;
  margin: 0 4px 0 0;
}

.e4s-footer--primary .e4s-footer__icon {
  fill: var(--e4s-footer--primary__text-color);
}

.e4s-footer--ia-primary .e4s-footer__icon {
  fill: var(--e4s-footer--ia-primary__text-color);
}

.e4s-footer-social-media-icon__container > a {
  margin: 0 4px;
}

/*@media screen and (max-width: 768px) {*/
/*    .e4s-footer-links__grid {*/
/*        display: flex;*/
/*        flex-direction: column;*/
/*        gap: 40px;*/
/*    }*/

/*    .e4s-footer-links__container {*/
/*        align-items: center;*/
/*    }*/

/*    .e4s-footer-links__container-right {*/
/*        align-items: center;*/
/*    }*/
/*}*/

/*@media screen and (max-width: var(--e4s-width-controller__max-width)px) {*/
/*    .e4s-footer--wrapper {*/
/*        padding: 0;*/
/*    }*/
/*}*/

@media screen and (max-width: 1100px) {
  .e4s-footer--wrapper {
    padding: 0 16px;
  }
}

/*************************************************************************************************************************************
</FOOTER>
*************************************************************************************************************************************/

.e4s-show-only-large {
  display: block;
}

.e4s-show-only-mobile {
  display: none;
}

@media screen and (max-width: 768px) {
  .e4s-hide-mobile {
    display: none;
  }

  .e4s-show-only-mobile {
    display: block;
  }

  .e4s-show-only-large {
    display: none;
  }
}

/*************************************************************************************************************************************
<LOGIN>
*************************************************************************************************************************************/
.e4s-login--container {
  margin: 80px 0;
}

.e4s-card--login {
  flex-direction: column;
  min-width: 280px;
  max-width: 350px;
  width: 100%;
  height: fit-content;
  margin: 40px 0;
  padding: calc(var(--e4s-card--primary__padding) * 2);
}

.e4s-card--login > form > h1 {
  color: var(--slate-800);
}

.e4s-card--login__signup-container {
  margin: 12px 0 32px 0;
}

.e4s-card--login__signup-container > .e4s-body--100 {
  color: var(--slate-500);
}

.e4s-card--login-action-container {
  margin: 32px 0 0 0;
}

.e4s-card--login-action-container > button {
  width: 100%;
}

.e4s-card--login-action-container > p {
  margin: 8px 0 0 0;
}

.e4s-card--login-password-visible {
  background: var(--green-200);
}
/*************************************************************************************************************************************
</LOGIN>
*************************************************************************************************************************************/

/*.qaz-qaz svg path {*/
/*  fill: #0bbf0b;*/
/*}*/

.e4s-svg--default {
  fill: #5c5f62;
}

.e4s-svg--href {
  fill: var(--e4s-button--primary__background);
}

.e4s-svg--process-primary {
  fill: white;
}

.e4s-svg--process-secondary {
  fill: var(--e4s-button--primary__background);
}

.e4s-svg--process-tertiary {
  fill: var(--e4s-button--primary__background);
}

.e4s-age-group--standard {
  color: black;
}

.e4s-age-group--default {
  color: black;
}

.e4s-age-group--non-default {
  color: var(--e4s-info-section--warn__text-color);
}

@media print {
  .e4s-do-not-print {
    display: none;
  }
}
