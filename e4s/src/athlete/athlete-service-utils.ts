import {
  IAgeGroup,
  IAgeInfo,
  IAthlete,
  IAthleteOptions,
  IAthleteSearch,
  IAthleteSummary,
  IVetAgeGroup,
} from "./athlete-models";
import { GENDER } from "../common/common-models";

export function factoryAthleteSummary(): IAthleteSummary {
  return {
    id: 0,
    firstName: "",
    surName: "",
    URN: "",
    dob: "",
    club: "",
    clubname: "",
    clubid: 0,
    club2: "",
    club2Id: 0,
    club2id: 0,
    gender: GENDER.UNKNOWN,
    classification: 0,
    schoolid: 0,
    school: "",
    inTeam: false,
    ageInfo: factoryAgeInfo(),
    aocode: "",
    activeEndDate: "",
    userAthletes: [],
    events: [],
    image: "",
    options: factoryIAthleteOptions(),
    pbInfo: [],
    email: "",
    infoText: "",
  };
}

export function factoryIAthleteOptions(): IAthleteOptions {
  return {
    noEntryReason: "",
    emergency: {
      name: "",
      tel: "",
      relationship: "",
    },
    socials: {
      tiktok: "",
      instagram: "",
      facebook: "",
    },
    coach: "",
    trainingGroup: "",
    genericCompAthleteEntities: {},
  };
}

export function factoryAgeInfo(): IAgeInfo {
  return {
    ageGroup: factoryAgeGroup(),
    ageGroups: [],
    vetAgeGroup: factoryVetAgeGroup(),
    currentAge: 0,
    competitionAge: 0,
  };
}

export function factoryAgeGroup(): IAgeGroup {
  return {
    id: 0,
    Name: "",
    name: "",
    shortName: "",
  };
}

export function factoryVetAgeGroup(): IVetAgeGroup {
  return {
    Name: "",
    shortName: "",
  };
}

export function factoryGetAthlete(): IAthlete {
  return {
    ...factoryAthleteSummary(),
  };
}

export function factoryAthleteSearch(): IAthleteSearch {
  return {
    athleteid: 0,
    country: "",
    region: "",
    county: "",
    club: "",
    firstname: "",
    surname: "",
    gender: "",
    ageGroupId: "",
    ceid: 0,
    eventId: 0, //  E.g. for League, if Id passed, only search for athletes in current
    //  comp who have entered this event.

    search: "", //  If passed, "adds" to back end where statement.
    urn: "",
    showAllAthletes: "1", //  0 = only show eligible, 1 = show all athletes
  };
}
