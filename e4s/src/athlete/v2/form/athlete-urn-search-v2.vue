<template>
  <CardGenericV2>
    <div slot="all">
      <div class="e4s-flex-column e4s-gap--standard">
        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <FormGenericSectionTitleV2
            title-size="400"
            section-title="Find Existing Athlete!"
            section-overview=""
          />

          <ButtonGenericBackV2 v-if="showCancelButton" v-on:click="onCancel" />
        </div>

        <InfoSectionV2 info-type="info">
          <p>
            <InfoMinorSvg /> To speed up entry, we may be able to retrieve
            athlete details. Use the search if you know athlete's athletic
            organisation, registration number and date of birth, else manually
            fill in Athlete Profile.
          </p>
        </InfoSectionV2>
      </div>

      <div class="e4s-flex-column e4s-full-width">
        <FormGenericFieldGridV2>
          <template slot="content">
            <FormGenericInputTemplateV2 form-label="Association/ID">
              <template slot="field">
                <div class="e4s-flex-row e4s-gap--tiny">
                  <FieldSelectV2
                    style="width: 80px"
                    v-model="state.aoSelected"
                    :data-array="getAosForDropDown"
                  />
                  <FieldTextV2
                    style="width: 150px"
                    v-model="state.urnEntered"
                    v-on:keyup="urnChanged"
                  />
                </div>
              </template>
            </FormGenericInputTemplateV2>

            <!--            <FieldDropDownV2-->
            <!--              form-label="Association"-->
            <!--              v-model="state.aoSelected"-->
            <!--              :data-array="getAosForDropDown"-->
            <!--            />-->
            <!--            <FormGenericInputTextV2-->
            <!--              form-label="Association ID (URN)"-->
            <!--              v-model="state.urnEntered"-->
            <!--              v-on:keyup="urnChanged"-->
            <!--            />-->
            <!--            <FieldDateDropDownV2-->
            <!--              v-model="state.dobEntered"-->
            <!--              form-label="Date of Birth"-->
            <!--            />-->
            <FormGenericInputTemplateV2 form-label="Date of Birth">
              <a
                v-if="competitionBase.compId > 0"
                slot="after-label"
                href="#"
                @click.prevent="
                  state.showDobYears =
                    state.showDobYears === 'ALL' ? 'COMP' : 'ALL'
                "
                v-text="
                  'Show ' + (state.showDobYears === 'ALL' ? 'Comp' : 'All')
                "
              ></a>
              <template slot="field">
                <DateOfBirthV2
                  v-model="state.dobEntered"
                  :show-comp-years="state.showDobYears"
                  :competition-base="competitionBase"
                  :default-year="state.showDobYears === 'ALL' ? 2000 : ''"
                />
                <span
                  :class="
                    compAgeGroupMessage.isMatch
                      ? 'e4s-info-text--success'
                      : 'e4s-info-text--error'
                  "
                  v-text="compAgeGroupMessage.message"
                  v-if="compAgeGroupMessage.message.length > 0"
                ></span>
              </template>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2 form-label="Buttons" :hide-label="true">
              <template slot="field">
                <div
                  class="e4s-flex-row e4s-flex-wrap e4s-input--container"
                  style="gap: 8px"
                >
                  <LoadingSpinnerV2 v-if="state.isLoading" />
                  <ButtonGenericV2
                    button-type="tertiary"
                    text="Reset"
                    v-on:click="reset"
                  />

                  <ButtonGenericV2
                    button-type="primary"
                    text="Search"
                    v-on:click="getByUrn"
                    :disabled="isSearchButtonDisabled"
                  />
                </div>
              </template>
            </FormGenericInputTemplateV2>
          </template>
        </FormGenericFieldGridV2>
      </div>

      <InfoSectionV2 info-type="warn" v-if="state.responseMessage.length > 0">
        <p v-text="state.responseMessage"></p>
      </InfoSectionV2>
    </div>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  reactive,
  SetupContext,
  watch,
} from "@vue/composition-api";

import * as R from "ramda";
import { AthleticOrgService } from "../../../common/ui/athletic-org/athletic-org-service";
import DateEntry from "../../../common/ui/datetime/date-entry.vue";
import { IAthleticsOrganisation } from "../../../common/ui/athletic-org/athletic-org-models";
import { AthleteDataService } from "../../athlete-data-service";
import { AthleteService } from "../../athlete-service";
import {
  IBaseConcrete,
  IServerResponse,
  IsoDateTime,
} from "../../../common/common-models";
import { IAthlete } from "../../athlete-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import CardGenericV2 from "../../../common/ui/layoutV2/card-generic-v2.vue";
import FormGenericSectionTitleV2 from "../../../common/ui/layoutV2/form/form-generic-section-title-v2.vue";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FieldDropDownV2 from "../../../common/ui/layoutV2/fields/field-drop-down-v2.vue";
import FieldDateDropDownV2 from "../../../common/ui/layoutV2/fields/field-date-drop-down-v2.vue";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import InfoMinorSvg from "../../../common/ui/svg/InfoMinorSvg.vue";
import { simpleClone } from "../../../common/common-service-utils";
import FormGenericFieldGridV2 from "../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericBackV2 from "../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import DateOfBirthV2 from "./DateOfBirthV2.vue";
import { ICompetitionBase } from "../../../competition/competition-models";
import { factoryCompetitionBase } from "../../../competition/v2/competiton-service-v2";
import { apiCleanAthlete, getCompAgeGroupMessage } from "../athlete-service-v2";
import FieldSelectV2 from "../../../common/ui/layoutV2/fields/field-select-v2.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";

const athleticOrgService = new AthleticOrgService();

interface AthleteUrnSearchState {
  urnEntered: string;
  dobEntered: string;
  isLoading: boolean;
  responseMessage: string;
  athlete: IAthlete;
  aoSelected: {
    id: number;
    name: string;
  };
  showDobYears: "ALL" | "COMP";
}

export default defineComponent({
  name: "athlete-urn-search-v2",
  components: {
    FieldTextV2,
    FieldSelectV2,
    DateOfBirthV2,
    ButtonGenericBackV2,
    LoadingSpinnerV2,
    FormGenericInputTemplateV2,
    FormGenericFieldGridV2,
    InfoMinorSvg,
    InfoSectionV2,
    ButtonGenericV2,
    FieldDateDropDownV2,
    FieldDropDownV2,
    FormGenericInputTextV2,
    FormGenericSectionTitleV2,
    CardGenericV2,
    DateEntry,
  },
  props: {
    defaultAo: {
      type: Object as PropType<IAthleticsOrganisation>,
      default: () => {
        return athleticOrgService.factory();
      },
    },
    aos: {
      type: Array as PropType<IAthleticsOrganisation[]>,
      default: () => {
        return [];
      },
    },
    showCancelButton: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    competitionBase: {
      type: Object as PropType<ICompetitionBase>,
      default: () => {
        return factoryCompetitionBase();
      },
    },
  },
  setup(
    props: {
      defaultAo: IAthleticsOrganisation;
      aos: IAthleticsOrganisation[];
      showCancelButton: boolean;
      competitionBase: ICompetitionBase;
    },
    context: SetupContext
  ) {
    const athleteDataService: AthleteDataService = new AthleteDataService();
    const athleteService: AthleteService = new AthleteService();

    // athleticOrganisationSelected: athleticOrgService.factory(),
    const state = reactive<AthleteUrnSearchState>({
      urnEntered: "",
      dobEntered: "",
      isLoading: false,
      responseMessage: "",
      athlete: athleteService.factoryGetAthlete(),
      aoSelected: {
        id: 0,
        name: "",
      },
      showDobYears: "ALL",
    });

    // setDefault();

    if (props.competitionBase.compId > 0) {
      state.showDobYears = "COMP";
    }

    watch(
      () => props.defaultAo,
      () => {
        setDefault();
      },
      {
        immediate: true,
      }
    );

    watch(
      () => props.aos,
      () => {
        setDefault();
      },
      {
        immediate: true,
      }
    );

    function setDefault() {
      props.aos.forEach((athleticsOrganisation: IAthleticsOrganisation) => {
        if (athleticsOrganisation.id === props.defaultAo.id) {
          // state.athleticOrganisationSelected = athleticsOrganisation;
          state.aoSelected = simpleClone({
            id: athleticsOrganisation.id,
            name: athleticsOrganisation.code,
          });
        }
      });
    }

    function urnChanged() {}

    function dobSelected(dob: IsoDateTime) {
      state.dobEntered = dob;
    }

    const isSearchButtonDisabled = computed(() => {
      return !(
        state.urnEntered.length > 0 &&
        state.dobEntered.length > 0 &&
        !state.isLoading
      );
    });

    function getByUrn() {
      state.isLoading = true;
      state.responseMessage = "";
      state.athlete = athleteService.factoryGetAthlete();

      //  Reset...
      context.emit("onResponse", athleteService.factoryGetAthlete());
      athleteDataService
        .getAthleteByUrn(
          state.aoSelected.name,
          state.urnEntered,
          state.dobEntered
        )
        .then((response: IServerResponse<IAthlete>) => {
          if (response.errNo > 0) {
            if (response.errNo > 0) {
              state.responseMessage = response.error;
              return;
            }

            messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR);
            return;
          }

          // messageDispatchHelper("Record found.", USER_MESSAGE_LEVEL.INFO);
          state.athlete = apiCleanAthlete(response.data);
          state.responseMessage =
            "Athlete found, if not correct athlete use 'Reset' or search again.";
          context.emit("onResponse", R.clone(state.athlete));
        })
        .catch((error) => {
          messageDispatchHelper(
            "Error finding athlete: " + error,
            USER_MESSAGE_LEVEL.INFO
          );
        })
        .finally(() => {
          state.isLoading = false;
        });
    }

    function reset() {
      state.urnEntered = "";
      state.dobEntered = "";
      state.isLoading = false;
      state.responseMessage = "";
      state.athlete = athleteService.factoryGetAthlete();

      context.emit("onResponse", athleteService.factoryGetAthlete());
    }

    const getAosForDropDown = computed(() => {
      return props.aos.reduce<IBaseConcrete[]>((accum, ao) => {
        if (ao.liveLink === 1 && ao.code !== "INT") {
          accum.push({
            id: ao.id,
            name: ao.code,
          });
        }

        return accum;
      }, []);
    });

    function onCancel(athlete: IAthlete) {
      context.emit("onCancel");
    }

    const compAgeGroupMessage = computed(() => {
      return getCompAgeGroupMessage(state.dobEntered, props.competitionBase);
    });

    return {
      state,
      urnChanged,
      dobSelected,
      isSearchButtonDisabled,
      getByUrn,
      reset,
      getAosForDropDown,
      onCancel,
      compAgeGroupMessage,
    };
  },
});
</script>
