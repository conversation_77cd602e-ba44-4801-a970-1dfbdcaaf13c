<template>
  <!--  <CardGenericV2>-->
  <!--  <div slot="all">-->
  <div
    class="
      e4s-flex-row e4s-justify-flex-space-between
      e4s-gap--standard
      e4s-justify-flex-row-vert-center
    "
  >
    <div class="e4s-flex-column e4s-full-width">
      <div
        v-text="athleteEvent.eventname + ' @' + getEventTime"
        class="e4s-subheader--200"
      ></div>

      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <span>Order no</span>
        <PrimaryLink
          v-if="isAdmin"
          :linkText="athleteEvent.orderno"
          :link="
            '/wp-admin/post.php?post=' + athleteEvent.orderno + '&action=edit'
          "
          target="_order"
        />
        <!--        <a-->
        <!--          v-if="isAdmin"-->
        <!--          class="e4s-hyperlink&#45;&#45;100 e4s-hyperlink&#45;&#45;primary"-->
        <!--          target="_order"-->
        <!--          :href="-->
        <!--            '/wp-admin/post.php?post=' + athleteEvent.orderno + '&action=edit'-->
        <!--          "-->
        <!--        >-->
        <!--          <span v-text="athleteEvent.orderno"></span>-->
        <!--        </a>-->
        <span v-if="!isAdmin" v-text="athleteEvent.orderno"></span>
        <span v-text="getStatus"></span>
      </div>
    </div>
    <div class="e4s-flex-column">
      <ButtonGenericV2
        text="Options"
        :button-type="buttonType"
        v-on:click="selectedOptions"
      />
    </div>
  </div>
  <!--  </div>-->
  <!--  </CardGenericV2>-->
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import { useConfigStore } from "../../../../config/useConfigStore";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { IAthleteEvent } from "../../../athlete-event/athlete-event-models";
import {
  eventDateTimeDisplay,
  eventTimeDisplay,
} from "../../../../common/common-service-utils";
import { ButtonGenericType } from "../../../../common/ui/layoutV2/buttons/button-generic-models";
import { isBefore, parse } from "date-fns";
import PrimaryLink from "../../../../common/ui/layoutV2/href/PrimaryLink.vue";

export default defineComponent({
  name: "entries-grid-row-v2",
  components: {
    PrimaryLink,
    ButtonGenericV2,
    CardGenericV2,
  },
  props: {
    athleteEvent: {
      type: Object as PropType<IAthleteEvent>,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
    // isOptionsVisible: {
    //   type: Boolean,
    //   default: function () {
    //     /**
    //      * Don't hide button,  leave it visible but disabled.
    //      */
    //     return false;
    //   },
    // },
  },
  setup(
    props: {
      athleteEvent: IAthleteEvent;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const configStore = useConfigStore();

    const compDate = computed(() => {
      return parse(props.athleteEvent.compdate);
    });

    const getEventDateTime = computed(() => {
      return eventDateTimeDisplay(props.athleteEvent.eventtime);
    });

    const getEventTime = computed(() => {
      return eventTimeDisplay(props.athleteEvent.eventtime);
    });

    function cancel() {
      context.emit("cancel");
    }

    const getStatus = computed(() => {
      const paidObj = {
        0: "Not Paid",
        1: "Paid",
        2: "Awaiting Payment",
        3: "Removed",
      };
      const paid = props.athleteEvent.paid;
      const options = props.athleteEvent.options;
      if (paid === 0 && options) {
        if (options.cancelUser && options.cancelUser > 0) {
          return (
            "Cancelled by user: (" +
            options.cancelUser +
            ") Reason: " +
            (options.reason ? options.reason : "N/A")
          );
        }
        if (options.refunds && options.refunds.length > 0) {
          const refundMessages = options.refunds.reduce((accum, refund) => {
            accum.push("Refund. Reason: " + refund.reason);
            return accum;
          }, [] as string[]);
          return refundMessages.join(". ");
        }
      }
      return paidObj[paid] ? paidObj[paid] : "N/A";
    });

    function selectedOptions() {
      context.emit("selectedOptions", props.athleteEvent);
    }

    const stillBeforeCompDate = computed<boolean>(() => {
      // const now = new Date();
      return isBefore(new Date(), compDate.value);
    });

    const buttonType = computed<ButtonGenericType>(() => {
      //  Entry cancelled, but we'll still let them see the options.
      if (
        props.athleteEvent.options &&
        props.athleteEvent.options.cancelUser &&
        props.athleteEvent.options.cancelUser > 0
      ) {
        return "tertiary";
      }
      return isBefore(new Date(), compDate.value) ? "primary" : "tertiary";
    });

    return {
      cancel,
      configStore,
      getEventDateTime,
      getStatus,
      getEventTime,
      selectedOptions,
      buttonType,
      stillBeforeCompDate,
    };
  },
});
</script>
