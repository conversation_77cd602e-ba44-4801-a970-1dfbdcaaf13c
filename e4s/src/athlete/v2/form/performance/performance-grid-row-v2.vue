<template>
  <!--  <CardGenericV2>-->
  <!--    <div slot="all">-->
  <!--      <div class="e4s-flex-row e4s-justify-flex-space-between">-->
  <!--        <div v-text="athletePb.eventName" class="e4s-subheader&#45;&#45;200"></div>-->

  <!--        <EditTimePbV2 :edit-time-pb-input-v2="editTimePbInputV2" :show-label="false"/>-->
  <!--      </div>-->
  <!--    </div>-->
  <!--  </CardGenericV2>-->
  <div>
    <div v-text="athletePb.eventName" class="e4s-subheader--200"></div>
    <EditTimePbV2
      :edit-time-pb-input-v2="editTimePbInputV2"
      :show-label="false"
    />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { useConfigStore } from "../../../../config/useConfigStore";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import { IAthlete, IAthletePb } from "../../../athlete-models";
import { AthleteService } from "../../../athlete-service";
import { PBService } from "../../../../athleteCompSched/pb-service";
import EditTimePbV2 from "../../../../athleteCompSched/pb/v2/edit-time-pb-v2.vue";
import { EditTimePbInputV2 } from "../../../../athleteCompSched/pb/v2/edit-time-pb-models-v2";
import { AthleteCompSchedService } from "../../../../athleteCompSched/athletecompsched-service";

const athleteService = new AthleteService();

export default defineComponent({
  name: "performance-grid-row-v2",
  components: {
    EditTimePbV2,
    ButtonGenericV2,
  },
  props: {
    athlete: {
      type: Object as PropType<IAthlete>,
      required: true,
    },
    athletePb: {
      type: Object as PropType<IAthletePb>,
      default: () => {
        return athleteService.factoryAthletePb();
      },
    },
    isAdmin: {
      type: Boolean,
      default: function () {
        return false;
      },
    },
    athleteCompSchedService: {
      type: Object as PropType<AthleteCompSchedService>,
      required: true,
    },
  },
  setup(
    props: {
      athlete: IAthlete;
      athletePb: IAthletePb;
      isAdmin: boolean;
      athleteCompSchedService: AthleteCompSchedService;
    },
    context: SetupContext
  ) {
    const configStore = useConfigStore();
    const pbService = new PBService();

    /*
    watch(
      () => props.consult,
      (newValue: any, oldValue: any) => {
        console.log("");
      }
    );
     */

    function getDefaultDisplayUom(athletePb: IAthletePb): string {
      return athletePb.uomInfo.options[0].short;
    }

    function formatSomePb(
      pb: number | string | null,
      athletePb: IAthletePb
    ): string {
      if (typeof pb === "undefined" || !pb) {
        return "N/A";
      }
      const pbLocal = typeof pb === "string" ? Number(pb) : pb;

      if (pbLocal === 0) {
        return "N/A";
      }

      if (athletePb.uomInfo.type === "T") {
        const pattern = athletePb.uomInfo.options[0].pattern;
        return (
          pbService.convertSecondsToUserFormat(pbLocal, pattern) +
          getDefaultDisplayUom(athletePb)
        );
      } else {
        return pb.toString() + getDefaultDisplayUom(athletePb);
      }
    }

    const ep = computed(() => {
      return formatSomePb(props.athletePb.pb, props.athletePb);
    });

    const sb = computed(() => {
      return formatSomePb(props.athletePb.sb, props.athletePb);
    });

    const pb = computed(() => {
      return formatSomePb(props.athletePb.pof10pb, props.athletePb);
    });

    function cancel() {
      context.emit("cancel");
    }

    const editTimePbInputV2 = computed(() => {
      const editTimePbInputV2: EditTimePbInputV2 = {
        athlete: props.athlete,
        athleteCompSched: props.athleteCompSchedService.factory(),
      };
      return editTimePbInputV2;
    });

    return { cancel, configStore, ep, sb, pb, editTimePbInputV2 };
  },
});
</script>
