<template>
  <div>
    <LoadingSpinnerV2 v-if="isLoading" />
    <CompEventActions
      v-if="isDataLoaded"
      :comp-event-actions-result="compEventActionsResult"
      v-on:close="close"
    />
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop } from "vue-property-decorator";
import {
  CompEventActionsController,
  ICompEventActionsResult,
} from "./comp-event-actions-controller";
import CompEventActions from "./comp-event-actions.vue";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";

@Component({
  name: "comp-event-actions-wrapper",
  components: { LoadingSpinnerV2, CompEventActions },
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class CompEventActionsWrapper extends Vue {
  public readonly configApp!: IConfigApp;

  @Prop({
    required: true,
  })
  public readonly compId: number;

  @Prop({
    required: true,
  })
  public readonly athleteId: number;

  @Prop({
    required: true,
  })
  public readonly clubId: number;

  @Prop({
    required: true,
  })
  public readonly entryId: number;

  public isLoading = true;
  public compEventActionsController: CompEventActionsController =
    new CompEventActionsController();
  public compEventActionsResult: ICompEventActionsResult =
    this.compEventActionsController.factoryCompEventActionsResult();

  public created() {
    this.loadData();
  }

  public get isDataLoaded(): boolean {
    return this.compEventActionsResult.competitionSummaryPublic.compId > 0;
  }

  public loadData() {
    this.isLoading = true;
    this.compEventActionsController
      .loadData(
        this.compId,
        this.athleteId,
        this.clubId,
        this.entryId,
        this.configApp.userInfo.user
      )
      .then((response) => {
        this.compEventActionsResult = response;
        this.isLoading = false;
      });
  }

  public close(requiresParentReloadOnClose: boolean) {
    console.log("CompEventActionsWrapper close");
    this.$emit("close", requiresParentReloadOnClose);
  }
}
</script>
