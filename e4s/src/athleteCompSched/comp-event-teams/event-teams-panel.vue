<template>
  <div>
    <SchedInfo class="e4s-card-v1" :schedInfo="getSchedInfo">
      <div slot="extra">
        <UserTeamAccess
          v-if="getTeamEventsHaveSecurity"
          :user-info="userInfo"
          :competition="competition"
          :user-entity="userEntity"
          :event-team-headers="eventTeamHeaders"
          v-on:submitted="selfServiceSubmitted"
        />
        <EventTeamsFilter
          v-if="getShowFilter"
          :event-team-headers="eventTeamHeaders"
          v-on:onChange="onFilterChange"
        />

        <ButtonGenericV2
          v-if="isAdmin"
          text="Show Teams as Entity"
          class="e4s-button--auto e4s-button--admin"
          @click="showTeamsAsEntity = true"
        />
      </div>
    </SchedInfo>

    <ModalV2
      v-if="showTeamsAsEntity"
      :is-full-screen="true"
      :disable-scroll="true"
    >
      <template slot="body">
        <div class="e4s-card-v1 e4s-margin-top--standard">
          <div class="e4s-padding--standard">
            <TeamsAsEntity
              :competition-id="competition.id"
              @close="showTeamsAsEntity = false"
            />

            <ButtonGenericV2 text="Close" @click="showTeamsAsEntity = false" />
          </div>
        </div>
      </template>
    </ModalV2>

    <div v-if="eventTeamHeadersLoading">Loading...</div>

    <div
      v-for="(eventTeamHeader, index) in eventTeamHeadersInternal"
      v-if="!eventTeamHeadersLoading"
      :key="eventTeamHeader.id"
    >
      <EventTeamHeader
        :index="index"
        :competition="competition"
        :event-team-header="eventTeamHeader"
        :user-entities="getEntitiesForEvent(eventTeamHeader)"
        :suppress-add-team="getHasMaxClubInfoTeamsAdded"
      >
      </EventTeamHeader>
      <div class="e4s-card-standard-sep comp-event-team-sep"></div>
      <div class="e4s-section-padding-separator"></div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { ICompetitionInfo } from "../../competition/competition-models";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import {
  COMP_EVENT_TEAMS_STORE_CONST,
  ICompEventTeamsStoreState,
} from "./comp-event-store";
import EventTeamHeader from "./event-team-header.vue";
import SchedInfo from "../sched-info/sched-info.vue";
import { ISchedInfo, ISchedInfoDetail } from "../athletecompsched-models";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp, IEntity, IUserInfo } from "../../config/config-app-models";
import UserTeamAccess from "./user-team-access/user-team-access.vue";
import { IEventTeamHeader } from "./event-teams-models";
import { ConfigService } from "../../config/config-service";
import { EventTeamService } from "./event-team-service";
import { CompetitionService } from "../../competition/competiton-service";
import EventTeamsFilter from "./event-teams-filter/event-teams-filter.vue";
import * as ClubCompInfoService from "../../entry/v2/schools/clubCompInfoService";
import { IClubCompInfo } from "../../entry/v2/schools/clubCompInfo-models";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import TeamsAsEntity from "./as-entity/ui/TeamsAsEntity.vue";
import ModalV2 from "../../common/ui/layoutV2/modal/modal-v2.vue";

const configService: ConfigService = new ConfigService();
const eventTeamService: EventTeamService = new EventTeamService();
const competitionService: CompetitionService = new CompetitionService();

@Component({
  name: "event-teams-panel",
  components: {
    ModalV2,
    TeamsAsEntity,
    ButtonGenericV2,
    EventTeamsFilter,
    EventTeamHeader,
    SchedInfo,
    UserTeamAccess,
  },
  computed: {
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      competition: (state: IEntryStoreState) =>
        state.entryForm.selectedCompetition,
      clubCompInfo: (state: IEntryStoreState) => state.entryForm.clubCompInfo,
    }),
    ...mapState(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME,
      {
        eventTeamHeaders: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeaders,
        eventTeamHeadersLoading: (state: ICompEventTeamsStoreState) =>
          state.eventTeamHeadersLoading,
      }
    ),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
      userInfo: (state: IConfigStoreState) => state.configApp.userInfo,
      userEntity: (state: IConfigStoreState) => state.userEntity,
    }),
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
  },
})
export default class EventTeamsPanel extends Vue {
  public readonly competition: ICompetitionInfo;
  public readonly configApp: IConfigApp;
  public readonly userInfo: IUserInfo;
  public readonly userEntity: IEntity;
  public readonly eventTeamHeaders: IEventTeamHeader[];
  public readonly clubCompInfo: IClubCompInfo;
  public readonly isAdmin: boolean;

  public eventTeamHeadersLoading: boolean;

  public userEntities: IEntity[] = [];
  public eventTeamHeadersInternal: IEventTeamHeader[] = [];

  public filterTerm: string = "";

  public showTeamsAsEntity: boolean = false;

  public created() {
    this.eventTeamHeadersInternal = R.clone(this.eventTeamHeaders);
  }

  @Watch("eventTeamHeaders")
  public onEventTeamHeadersChanged(newValue: IEventTeamHeader[]) {
    this.eventTeamHeadersInternal = R.clone(newValue);
  }

  @Watch("competition")
  public onCompetitionChanged(
    newValue: ICompetitionInfo,
    oldValue: ICompetitionInfo
  ) {
    //  weird super old legacything, not sure why it's here...
    //  teams were not being reloaded when entering..
    // if (newValue && newValue.id && newValue.id !== oldValue.id) {
    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
      { compId: this.competition.id }
    );
    // }
  }

  @Watch("userInfo")
  public onUserInfoChanged(newValue: IUserInfo) {
    this.userEntities = configService.getEntitiesFromUserInfo(newValue);
  }

  public getEntitiesForEvent(eventTeamHeader: IEventTeamHeader): IEntity[] {
    if (
      eventTeamService.doesUserHaveAccessToThisEvent(
        this.userInfo,
        eventTeamHeader
      )
    ) {
      const isSchool = competitionService.isSchool(this.competition);
      return eventTeamService.getEntitiesForEvent(
        this.userInfo,
        isSchool,
        eventTeamHeader
      );
    }
    return [configService.factoryEntity()];
  }

  public reloadTeamEvents() {
    this.$store.dispatch(
      COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
        "/" +
        COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
      { compId: this.competition.id }
    );
  }

  public get getSchedInfo() {
    const schedInfoDetails: ISchedInfoDetail[] = [];

    const schedInfoDetail: ISchedInfoDetail = {
      title: "",
      body:
        this.competition &&
        this.competition.id > 0 &&
        this.competition.options.helpText &&
        this.competition.options.helpText.teams
          ? this.competition.options.helpText.teams
          : "",
    };

    schedInfoDetails.push(schedInfoDetail);

    // const userAccessMessage: string = eventTeamService.userAccessMessage(this.userInfo);
    //
    // if (userAccessMessage.length > 0 ) {
    //     const schedInfoDetailAccess: ISchedInfoDetail = {
    //         title: "User Access",
    //         body: userAccessMessage
    //     };
    //     schedInfoDetails.push(schedInfoDetailAccess);
    // }

    const schedInfo: ISchedInfo = {
      title: "Team Events",
    } as ISchedInfo;
    schedInfo.schedInfoDetails = schedInfoDetails;
    schedInfo.autoExpand = true;
    schedInfo.shortDescription = "";
    schedInfo.showLinks = false;

    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      const clubCompInfo: IClubCompInfo = this.clubCompInfo as IClubCompInfo;
      schedInfo.schedInfoDetails.push({
        title: "",
        body: ClubCompInfoService.userMessageTeamsHtml(
          clubCompInfo,
          this.eventTeamHeadersInternal
        ),
      });
    }

    return schedInfo;
  }

  public get getHasMaxClubInfoTeamsAdded(): any {
    if (
      ClubCompInfoService.hasClubCompInfoCompetition(
        this.clubCompInfo as IClubCompInfo
      )
    ) {
      const clubCompInfo: IClubCompInfo = this.clubCompInfo as IClubCompInfo;
      return (
        clubCompInfo.entryData.teamCnt >= clubCompInfo.configData.maxRelays
      );
    }
    return false;
  }

  public onFilterChange(eventTeamHeaders: IEventTeamHeader[]) {
    this.eventTeamHeadersInternal = eventTeamHeaders;
  }

  public get getShowFilter(): boolean {
    return this.eventTeamHeaders.length > 0;
  }

  public selfServiceSubmitted() {
    this.$store.commit(
      ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
        "/" +
        ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_ATHLETE_TRIGGER_REFRESH
    );
  }

  public get getTeamEventsHaveSecurity(): boolean {
    return eventTeamService.getTeamEventsHaveSecurity(this.eventTeamHeaders);
  }
}
</script>
