import { IFormRow } from "../../athletecompsched-models";
import { ICompEventTeam, IEventTeamHeader } from "../event-teams-models";
import { AthleteService } from "../../../athlete/athlete-service";

const athleteService: AthleteService = new AthleteService();

export class LeagueService {
  public getFormRows(
    eventTeamHeader: IEventTeamHeader,
    compEventTeam: ICompEventTeam
  ): IFormRow[] {
    if (compEventTeam && compEventTeam.formRows && compEventTeam.prodId > 0) {
      const formRows = compEventTeam.formRows;
      formRows.sort((a: IFormRow, b: IFormRow): number => {
        return a.position - b.position;
      });
      return formRows;
    }
    if (
      eventTeamHeader &&
      eventTeamHeader.ceoptions &&
      eventTeamHeader.ceoptions.eventTeam &&
      eventTeamHeader.ceoptions.eventTeam.formRows
    ) {
      const formRows = eventTeamHeader.ceoptions.eventTeam.formRows;
      formRows.sort((a: IFormRow, b: IFormRow): number => {
        return a.position - b.position;
      });

      return formRows;
    }
    return [];
  }

  public hasAthlete(formRow: IFormRow): boolean {
    return formRow.athlete && formRow.athlete.firstName ? true : false;
  }

  public checkAthletesOk(formRows: IFormRow[]): IFormRow[] {
    return formRows.map((formRow) => {
      if (!this.hasAthlete(formRow)) {
        formRow.athlete = athleteService.factoryAthleteSummary();
      }
      return formRow;
    });
  }
}
