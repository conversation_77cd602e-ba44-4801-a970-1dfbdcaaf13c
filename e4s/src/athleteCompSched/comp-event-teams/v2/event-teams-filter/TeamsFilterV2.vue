<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div class="e4s-flex-row">
      <div class="e4s-flex-column e4s-flex-grow">
        <h3>Team Filters</h3>
      </div>
    </div>

    <!--    {{ state.filter }}-->

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2
          class="e4s-flex-grow"
          form-label="Search Text"
        >
          <InputDebounce
            slot="field"
            v-model="state.filter.freeText"
            :default-value="state.filter.freeText"
            @onChange="onChangeFreeText"
          />
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2 class="e4s-flex-grow" form-label="Event">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="state.filter.eventName"
            @change="onFilterChange"
          >
            <option
              v-for="eventName in eventNames"
              :key="eventName"
              :value="eventName"
              v-text="eventName === '' ? 'ALL' : eventName"
            ></option>
          </select>
        </FormGenericInputTemplateV2>

        <!--        <FormGenericInputTemplateV2-->
        <!--          class="e4s-flex-grow"-->
        <!--          form-label="Teams Entered"-->
        <!--        >-->
        <!--          <select-->
        <!--            slot="field"-->
        <!--            class="browser-default e4s-input-field e4s-input-field&#45;&#45;primary"-->
        <!--            v-model="state.filter.hasTeams"-->
        <!--            @change="onFilterChange"-->
        <!--          >-->
        <!--            <option value="ALL">ALL</option>-->
        <!--            <option value="WITH">With Teams</option>-->
        <!--            <option value="WITHOUT">Without Teams</option>-->
        <!--          </select>-->
        <!--        </FormGenericInputTemplateV2>-->
      </template>
    </FormGenericFieldGridV2>

    <FormGenericFieldGridV2>
      <template slot="content">
        <FormGenericInputTemplateV2 class="e4s-flex-grow" form-label="Gender">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="state.filter.gender"
            @change="onFilterChange"
          >
            <option :value="''">ALL</option>
            <option :value="GENDER.FEMALE">Female</option>
            <option :value="GENDER.MALE">Male</option>
          </select>
        </FormGenericInputTemplateV2>

        <FormGenericInputTemplateV2
          class="e4s-flex-grow"
          form-label="Age Group"
        >
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="state.filter.ageGroup"
            @change="onFilterChange"
          >
            <option
              v-for="ageGroup in ageGroups"
              :key="ageGroup.id"
              :value="ageGroup"
              v-text="ageGroup.name"
            ></option>
          </select>
        </FormGenericInputTemplateV2>
      </template>
    </FormGenericFieldGridV2>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  PropType,
  watch,
} from "@vue/composition-api";
import { IEventTeamHeader } from "../../event-teams-models";
import { EventTeamsFilterService } from "../../event-teams-filter/event-teams-service";
import { GENDER, IObjectKeyType } from "../../../../common/common-models";
import { IAgeGroupBase } from "../../../../agegroup/agegroup-models";
import { CommonService } from "../../../../common/common-service";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import FormGenericFieldGridV2 from "../../../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import { IEventTeamHeaderFilterV2 } from "../../event-teams-filter/event-teams-filter-models";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import InputDebounce from "../../../../common/ui/field/input-debounce.vue";

const eventTeamsFilterService = new EventTeamsFilterService();
const commonService = new CommonService();

export default defineComponent({
  name: "TeamsFilterV2",
  components: {
    InputDebounce,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
    FormGenericInputTemplateV2,
  },
  props: {
    eventTeamHeaderFilter: {
      type: Object as PropType<IEventTeamHeaderFilterV2>,
      default: () => {
        return eventTeamsFilterService.factoryEventTeamHeaderFilterV2();
      },
    },
    eventTeamHeaders: {
      type: Array as PropType<IEventTeamHeader[]>,
      default: () => [],
    },
  },
  setup(
    props: {
      eventTeamHeaderFilter: IEventTeamHeaderFilterV2;
      eventTeamHeaders: IEventTeamHeader[];
    },
    { emit }
  ) {
    const state = reactive({
      filter: eventTeamsFilterService.factoryEventTeamHeaderFilterV2(),
      filteredHeaders: [] as IEventTeamHeader[],
    });

    // set up a debounce param for text search

    // Initialize filtered headers
    state.filter = simpleClone(props.eventTeamHeaderFilter);
    state.filteredHeaders = simpleClone(props.eventTeamHeaders);

    const eventNames = computed((): string[] => {
      const eventObj: Record<string, string> = props.eventTeamHeaders.reduce<
        Record<string, string>
      >((accum, eventTeamHeader) => {
        const key = eventTeamHeader.eventGroup.name;
        if (!accum[key]) {
          accum[key] = key;
        }
        return accum;
      }, {});

      const names: string[] = commonService
        .convertObjectToArray(eventObj)
        .sort((a, b) => a.localeCompare(b));

      names.unshift("");
      return names;
    });

    const ageGroups = computed((): IAgeGroupBase[] => {
      const ageGroupBaseObj: IObjectKeyType<IAgeGroupBase> =
        props.eventTeamHeaders.reduce((accum, eventTeamHeader) => {
          const agId = eventTeamHeader.ageGroup.id;
          if (!accum[agId]) {
            accum[agId] = eventTeamHeader.ageGroup;
          }
          return accum;
        }, {} as IObjectKeyType<IAgeGroupBase>);

      const agAll: IAgeGroupBase = {
        id: 0,
        name: "ALL",
      };

      const groups: IAgeGroupBase[] = commonService
        .convertObjectToArray(ageGroupBaseObj)
        .sort((a, b) => a.name.localeCompare(b.name));

      groups.unshift(agAll);
      return groups;
    });

    watch(
      () => props.eventTeamHeaders,
      (newHeaders) => {
        state.filteredHeaders = simpleClone(newHeaders);
      }
    );

    watch(
      () => props.eventTeamHeaderFilter,
      (newFilter) => {
        console.error("TeamsFilterV2.watch.eventTeamHeaderFilter", newFilter);
        state.filter = simpleClone(newFilter);
      },
      { deep: true }
    );

    function onChangeFreeText(value: string) {
      console.log("TeamsFilterV2.onChangeFreeText", value);
      debugger;
      state.filter.freeText = value;
      onFilterChange();
    }

    function onFilterChange() {
      // state.filteredHeaders = eventTeamService.filterEventTeamHeaders(
      //   state.filter,
      //   props.eventTeamHeaders
      // );
      // console.log("TeamsFilterV2.onFilterChange", state.filteredHeaders);
      console.log("TeamsFilterV2.onFilterChange", state.filter);
      const newFilters: IEventTeamHeaderFilterV2 = state.filter;
      const payload = {
        options: simpleClone(newFilters),
        startSearch: false,
      };
      emit("onChange", payload);
    }

    return {
      state,
      eventNames,
      ageGroups,
      onChangeFreeText,
      onFilterChange,
      GENDER,
    };
  },
});
</script>

<style scoped>
/* Component-specific styles can be added here */
</style>
