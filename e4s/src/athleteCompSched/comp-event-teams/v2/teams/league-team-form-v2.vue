<template>
  <CardGenericV2>
    <div slot="content" class="e4s-flex-column e4s-gap--standard">
      <div class="e4s-flex-row e4s-justify-flex-space-between e4s-flex-start">
        <div class="e4s-flex-column">
          <EventTeamHeaderNameV2
            class="e4s-input--label"
            :event-team-header="eventTeamHeader"
          />
          <p
            v-if="userEntity.entityLevel > 0"
            class="e4s-subheader--general e4s-subheader--500"
            v-text="userEntity.name"
          ></p>
        </div>

        <EventTeamHeaderStatusPillV2 :event-team-header="eventTeamHeader" />
      </div>

      <div class="e4s-flex-row">
        <FormGenericInputTextV2
          class="e4s-full-width"
          form-label="Team Name"
          v-model="state.compEventTeam.teamName"
          :is-disabled="!teamController.state.canEditTeamName"
        >
          <span
            slot="after-label"
            class="e4s-subheader--general e4s-flex-row--end"
            v-text="eventTeamHeader.ceoptions.eventTeam.formType"
          ></span>
        </FormGenericInputTextV2>
      </div>

      <div
        class="e4s-flex-row e4s-full-width e4s-gap--standard"
        v-if="!state.showEligibility"
      >
        Can't find athlete?
        <!--        <a-->
        <!--          class="anchor-buttons"-->
        <!--          href="#"-->
        <!--          v-on:click.prevent="state.showEligibility = true"-->
        <!--        >-->
        <!--          Click here to check why.-->
        <!--        </a>-->
        <PrimaryLink
          link-text="Click here to check why."
          @onClick="showEligibility = true"
        />
      </div>

      <EventAthleteEligibleV2
        v-if="state.showEligibility"
        :ceid="eventTeamHeader.id"
        v-on:cancel="state.showEligibility = false"
        :comp-event-team="state.compEventTeam"
      />

      <FormGenericInputTextV2
        class="e4s-full-width"
        :form-label="state.options.other1.label"
        v-model="state.options.other1.value"
      >
      </FormGenericInputTextV2>

      <FormGenericInputTextV2
        class="e4s-full-width"
        :form-label="state.options.other2.label"
        v-model="state.options.other2.value"
      >
      </FormGenericInputTextV2>

      <div
        v-for="(formRow, index) in state.formRows"
        :key="index"
        class="e4s-flex-row e4s-gap--standard e4s-full-width"
      >
        <FormGenericInputTemplateV2
          :form-label="getTeamPositionNameEditMode(formRow)"
          class="e4s-full-width"
        >
          <template slot="field">
            <AthleteTypeAheadV2
              v-if="editMode"
              :competition-id="competition.compId"
              :disabled="isLoading"
              :user-entity="userEntity"
              :gender="eventTeamHeader.gender"
              :age-group-ids="getAgeGroupIds"
              :field-label="'Enter name or URN'"
              :position="index"
              :athlete-default="getDefaultAthlete(formRow)"
              :is-disabled="isLoading"
              place-holder=""
              @athleteSelected="athleteSelected"
              @reset="athleteRemoved"
            />
            <div v-else class="e4s-flex-row e4s-justify-flex-end">
              <span v-text="getAthleteName(formRow)"></span>
            </div>
          </template>
        </FormGenericInputTemplateV2>
      </div>

      <div class="e4s-flex-column e4s-info-text--error">
        <div
          v-for="message in state.userMessages"
          :key="message.id"
          v-text="message.message"
        ></div>
      </div>

      <EventTeamButtonsV2
        :comp-event-team="state.compEventTeam"
        :competition-summary="competition"
        :is-loading="isLoading"
        :edit-mode="editMode"
        :is-user-owner="teamController.state.isUserOwner"
        :is-submit-disabled="isLoading"
        @cancel="cancelCompEventTeam"
        @edit="editCompEventTeam"
        @delete="deleteCompEventTeam"
        @submit="submitCompEventTeam"
        @addUserCart="addUserCart"
      />
    </div>
  </CardGenericV2>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  PropType,
} from "@vue/composition-api";
import { format, parse } from "date-fns";
import { ICompEventTeam, IEventTeamHeader } from "../../event-teams-models";
import { IFormRow } from "../../../athletecompsched-models";
import { IUserMessage } from "../../../../user-message/user-message-models";
import { IEntity } from "../../../../config/config-app-models";
import { IAthleteSummary } from "../../../../athlete/athlete-models";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";

// Services
import { EventTeamService } from "../../event-team-service";
import { LeagueService } from "../../league/league-service";
import { AthleteService } from "../../../../athlete/athlete-service";
import { CommonService } from "../../../../common/common-service";
import { ConfigService } from "../../../../config/config-service";

// Components
import CardGenericV2 from "../../../../common/ui/layoutV2/card-generic-v2.vue";
import FormGenericInputTextV2 from "../../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import EventTeamHeaderNameV2 from "../event-team-header-name-v2.vue";
import EventTeamHeaderStatusPillV2 from "../event-team-header-status-pill-v2.vue";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import AthleteTypeAheadV2 from "../athlete-type-ahead-v2.vue";
import EventAthleteEligibleV2 from "./event-athlete-eligible-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
// import { useConfigController } from "../../../../config/useConfigStore";
import EventTeamButtonsV2 from "../event-team-buttons-v2.vue";
import { useTeamController } from "../useTeamController";
import PrimaryLink from "../../../../common/ui/layoutV2/href/PrimaryLink.vue";

const eventTeamService = new EventTeamService();
const leagueService = new LeagueService();
const athleteService = new AthleteService();
const commonService = new CommonService();
const configService = new ConfigService();

export default defineComponent({
  name: "LeagueTeamFormV2",
  components: {
    PrimaryLink,
    EventTeamButtonsV2,
    CardGenericV2,
    FormGenericInputTextV2,
    ButtonGenericV2,
    EventTeamHeaderNameV2,
    EventTeamHeaderStatusPillV2,
    FormGenericInputTemplateV2,
    AthleteTypeAheadV2,
    EventAthleteEligibleV2,
  },
  props: {
    competition: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    compEventTeamProp: {
      type: Object as PropType<ICompEventTeam>,
      required: true,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isNew: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    eventTeamHeader: {
      type: Object as PropType<IEventTeamHeader>,
      required: true,
    },
    eventTeamHeaders: {
      type: Array as PropType<IEventTeamHeader[]>,
      required: true,
    },
    userEntity: {
      type: Object as PropType<IEntity>,
      default: () => configService.factoryEntity(),
    },
  },
  setup(props, { emit }) {
    // const configController = useConfigController();

    const teamController = useTeamController();
    teamController.init({
      competition: {
        compId: props.competition.compId,
        compOrgId: props.competition.compOrgId,
      },
      eventTeamHeader: props.eventTeamHeader,
      compEventTeam: props.compEventTeamProp,
      userEntity: props.userEntity,
    });

    const state = reactive({
      compEventTeam: simpleClone(props.compEventTeamProp),
      formRows: [] as IFormRow[],
      userMessages: [] as IUserMessage[],
      showEligibility: false,
      athleteForAddButtonToShow: 0,
      options: {
        other1: {
          value: "",
          label: "Other 1",
        },
        other2: {
          value: "",
          label: "Other 2",
        },
      },
    });

    // Initialize form rows
    function initForm() {
      state.compEventTeam = simpleClone(props.compEventTeamProp);
      state.compEventTeam.ceid = props.eventTeamHeader.id;
      state.formRows = leagueService.getFormRows(
        props.eventTeamHeader,
        state.compEventTeam
      );
      state.formRows = leagueService.checkAthletesOk(state.formRows);
    }

    // Get age group IDs for athlete filtering
    const getAgeGroupIds = computed(() => {
      return eventTeamService.getOptionsAgeGroupIdsForAthleteSearch(
        props.eventTeamHeader
      );

      // return props.eventTeamHeader.ageGroupIds || [];
    });

    // Get team position name for edit mode
    function getTeamPositionNameEditMode(formRow: IFormRow) {
      if (!formRow) {
        return "";
      }
      let evtDate = "";
      let evtTime = "";
      if (formRow.dateTime.length > 0) {
        evtDate = format(parse(formRow.dateTime), "Do MMM");
        evtTime = commonService.getE4sStandardTimeOutPut(formRow.dateTime);
        evtTime = evtTime === "00:00" ? "TBC" : evtTime;
      }
      return formRow.eventDef.name + " [" + evtDate + " @" + evtTime + "]";
    }

    // Get default athlete for a form row
    function getDefaultAthlete(formRow: IFormRow): IAthleteSummary {
      if (formRow.athlete && formRow.athlete.firstName) {
        return formRow.athlete;
      }
      return athleteService.factoryAthleteSummary();
    }

    // Get athlete name for display
    function getAthleteName(formRow: IFormRow) {
      if (!formRow || !formRow.athlete) {
        return "";
      }
      let athleteName = "";
      if (formRow.athlete.id > 0) {
        athleteName = formRow.athlete.firstName + " " + formRow.athlete.surName;
      }
      return athleteName;
    }

    // Handle athlete selection
    function athleteSelected(payload: {
      athlete: IAthleteSummary;
      position: number;
    }) {
      state.userMessages = [];
      console.log("LeagueTeamFormV2.athleteSelected", payload);
      setAthlete(payload.athlete, payload.position);
    }

    // Handle athlete row selection
    function athleteRowSelected(index: number) {
      console.log("LeagueTeamFormV2.athleteRowSelected() index: " + index);
      state.athleteForAddButtonToShow = index;
    }

    // Set athlete in form row
    function setAthlete(athlete: IAthleteSummary, position: number) {
      console.log("LeagueTeamFormV2.setAthlete: " + position, athlete);

      const formRow = state.formRows[position];
      formRow.athlete = simpleClone(athlete);
      onFieldChanged();
    }

    // Handle athlete removal
    function athleteRemoved(position: number) {
      console.log("LeagueTeamFormV2.athleteRemoved: " + position);
      const formRow = state.formRows[position];
      formRow.athlete = athleteService.factoryAthleteSummary();
      onFieldChanged();
    }

    // Handle field changes
    function onFieldChanged() {
      state.compEventTeam.formRows = state.formRows;
      emit("changed", state.compEventTeam);
    }

    // Handle team name changes
    function onTeamNameChanged(teamName: string) {
      state.compEventTeam.teamName = teamName;
      onFieldChanged();
    }

    // Submit the team
    function submitCompEventTeam() {
      console.log("LeagueTeamFormV2.submitCompEventTeam()");
      state.userMessages = [];
      const userMessages: IUserMessage[] = eventTeamService.validateLeagueTeam(
        props.eventTeamHeader,
        state.compEventTeam
      );
      if (userMessages.length > 0) {
        state.userMessages = userMessages;
        return;
      }
      const compEventTeam = simpleClone(state.compEventTeam);
      emit("submit", compEventTeam);
    }

    // Cancel editing
    function cancelCompEventTeam() {
      emit("cancel");
    }

    // Edit team
    function editCompEventTeam() {
      emit("edit");
    }

    // Delete team
    function deleteCompEventTeam() {
      emit("delete", state.compEventTeam);
    }

    // Add to user cart
    function addUserCart() {
      emit("addUserCart", state.compEventTeam);
    }

    // const isUserOwner = computed(() => {
    //   return props.compEventTeamProp.entityId === props.userEntity.id;
    // });

    // const canEditTeamName = computed(() => {
    //   return (
    //     (props.eventTeamHeader.ceoptions.eventTeam.disableTeamNameEdit &&
    //       (props.isNew || isUserOwner.value)) ||
    //     configController.isAdmin.value
    //   );
    // });

    // Watch for prop changes
    watch(
      () => props.compEventTeamProp,
      (newValue) => {
        state.compEventTeam = simpleClone(newValue);
        initForm();
      }
    );

    watch(
      () => props.eventTeamHeader,
      () => {
        initForm();
      }
    );

    // Initialize on creation
    initForm();

    return {
      state,
      teamController,
      getAgeGroupIds,

      // canEditTeamName,
      // isUserOwner,
      athleteRowSelected,

      getTeamPositionNameEditMode,
      getDefaultAthlete,
      getAthleteName,
      athleteSelected,
      athleteRemoved,
      onTeamNameChanged,
      submitCompEventTeam,
      cancelCompEventTeam,
      editCompEventTeam,
      deleteCompEventTeam,
      addUserCart,
    };
  },
});
</script>

<style scoped>
.league-team--read-line {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
