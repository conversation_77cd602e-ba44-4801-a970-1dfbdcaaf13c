// import { simpleClone, unique } from "../../common/common-service-utils";
// import { mockChildEventGenderMerged } from "./mock/multi-event-options-compevents";
// import * as MultiEventOptionsService from "./multi-event-options-service";

describe("multi-event-options-service", () => {
  it("should be defined", () => {
    expect(true).toBe(true);
  });

  // it("filterCompEventsByEventGroupId", () => {
  //   const data = simpleClone(mockChildEventGenderMerged);
  //
  //   const res = MultiEventOptionsService.filterCompEventsByEventGroupId(
  //     5522,
  //     data
  //   );
  //   expect(res.length).toBe(16);
  //
  //   const childEvents = MultiEventOptionsService.getAllChildEvents(res);
  //   expect(childEvents.length).toBe(64);
  //
  //   const chileEventsEgIds = childEvents.map((ce) => ce.ceId);
  //   expect(unique(chileEventsEgIds).length).toBe(16);
  //
  //   const multiChildEvents =
  //     MultiEventOptionsService.getMultiChildEventsFromAllCompEvents(5522, data);
  //   // expect(multiChildEvents.length).toBe(16);
  //
  //   expect(unique(multiChildEvents)).toBe(16);
  //
  //   // expect(multiChildEvents).to  Be(16);
  //
  //   const eventGenderMap = multiChildEvents.reduce((accum, ce) => {
  //     let key: string = ce.ceId.toString();
  //     const desc =
  //       ce.name +
  //       "-" +
  //       ce.gender +
  //       ", eg: " +
  //       ce.egId +
  //       ", ce: " +
  //       ce.ceId +
  //       ", ev: " +
  //       ce.eventId;
  //     const exists = accum[key];
  //     if (exists) {
  //       key = key + "-" + 1;
  //     }
  //     accum[key] = desc;
  //     return accum;
  //   }, {} as Record<string, string>);
  //
  //   expect(eventGenderMap).toBe(16);
  // });
});
