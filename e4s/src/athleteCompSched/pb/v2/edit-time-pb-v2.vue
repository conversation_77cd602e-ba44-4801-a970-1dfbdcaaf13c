<template>
  <FormGenericInputTemplateV2
    class="e4s-flex-row--end qaz2"
    :form-label="formLabel"
    form-label-class="e4s-subheader--200 e4s-label--black"
    :show-label="showLabel"
  >
    <template slot="field">
      <div class="e4s-flex-column e4s-gap--standard">
        <InputWithButton
          v-if="showSave"
          class="e4s-flex-row--end-x e4s-flex-end qaz3"
        >
          <InputDebounce
            slot="field"
            class="e4s-input-field--100"
            style="
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
              margin-bottom: 0;
            "
            :default-value="editTimePbModelController.state.pbUserEntry"
            placeholder=""
            v-on:input="onUserEditedPb"
          />
          <template slot="after">
            <ButtonGenericV2
              class="e4s-button--75"
              style="width: 75px"
              :text="saveButtonText"
              :button-type="
                editTimePbModelController.state.isDirty
                  ? 'primary'
                  : 'secondary'
              "
              with-input="right"
              v-on:click="editTimePbModelController.save"
            >
            </ButtonGenericV2>
            <LoadingSpinnerV2
              v-if="editTimePbModelController.state.isLoading"
            />
          </template>
        </InputWithButton>

        <p
          v-if="editTimePbModelController.state.pbEditMessage.length > 0"
          class="e4s-body--200-x e4s-body--error"
          v-text="editTimePbModelController.state.pbEditMessage"
        ></p>
      </div>

      <InputDebounce
        v-if="!showSave"
        class="e4s-input-field--100"
        :default-value="editTimePbModelController.state.pbUserEntry"
        placeholder=""
        v-on:input="onUserEditedPb"
      />
    </template>
  </FormGenericInputTemplateV2>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { AthleteService } from "../../../athlete/athlete-service";
import { AthleteCompSchedService } from "../../athletecompsched-service";
import { useEditTimePbModelController } from "./useEditTimePbModelController";
import { EditTimePbInputV2 } from "./edit-time-pb-models-v2";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import InputDebounce from "../../../common/ui/field/input-debounce.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import { IPbSubmitState } from "../../../athlete/athlete-data";

const athleteService: AthleteService = new AthleteService();
const athleteCompSchedService: AthleteCompSchedService =
  new AthleteCompSchedService();

export default defineComponent({
  name: "edit-time-pb-v2",
  components: {
    FormGenericInputTemplateV2,
    LoadingSpinnerV2,
    InputDebounce,
    InputWithButton,
    ButtonGenericV2,
  },
  props: {
    editTimePbInputV2: {
      type: Object as PropType<EditTimePbInputV2>,
      default: function () {
        const editTimePbInputV2: EditTimePbInputV2 = {
          athlete: athleteService.factoryGetAthlete(),
          athleteCompSched: athleteCompSchedService.factory(),
        };
        return editTimePbInputV2;
      },
    },
    formLabel: {
      type: String,
      default: function () {
        return "Estimated Performance";
      },
    },
    showLabel: {
      type: Boolean,
      default: () => {
        //   If false hides the whole label section, which doesn't
        //  really make sense...you'd just dump an input or whatever
        //  on ui...see "hideLabel"
        return true;
      },
    },
    showSave: {
      type: Boolean,
      default: () => {
        //   If false hides the whole label section, which doesn't
        //  really make sense...you'd just dump an input or whatever
        //  on ui...see "hideLabel"
        return true;
      },
    },
    saveButtonText: {
      type: String,
      default: function () {
        return "Save";
      },
    },
  },
  setup(
    props: { editTimePbInputV2: EditTimePbInputV2 },
    context: SetupContext
  ) {
    const editTimePbModelController = useEditTimePbModelController();

    watch(
      () => props.editTimePbInputV2,
      (newValue: EditTimePbInputV2) => {
        editTimePbModelController.init(newValue);
      },
      {
        immediate: true,
      }
    );

    function onUserEditedPb(userInput: string) {
      editTimePbModelController.onUserEditedPb(userInput);
      if (editTimePbModelController.state.pbEditMessage === "") {
        const pbSubmitState: IPbSubmitState = {
          aid: editTimePbModelController.state.editTimePbInputV2.athlete.id,
          eid: editTimePbModelController.state.editTimePbInputV2
            .athleteCompSched.eventid,
          pb: editTimePbModelController.state.processPb.pb,
          pbText: editTimePbModelController.state.pbUserEntry,
          trackSb: true,
        };
        context.emit("onUserEditedPb", pbSubmitState);
      }
    }

    return { editTimePbModelController, onUserEditedPb };
  },
});
</script>
