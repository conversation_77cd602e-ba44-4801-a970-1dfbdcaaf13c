<template>
  <div class="e4s-card e4s-card--login" data-testid="forgot-password-form">
    <h1 class="e4s-header--400 e4s-flex-start">
      Password reset request for Entry4Sports
    </h1>
    <div class="e4s-flex-row e4s-card--login__signup-container">
      <p class="e4s-body--100">
        An email will be sent to the address entered below with a reset link.
        <a
          :href="getLoginHref"
          class="e4s-hyperlink--100 e4s-hyperlink--primary"
          data-testid="back-to-login-link"
        >
          Back to Login
        </a>
      </p>
    </div>

    <FormGenericInputTemplateV2
      form-label="Email"
      :error-message="getEmailError"
    >
      <template slot="field">
        <!--        <InputWithButton>-->
        <FieldTextV2
          v-model="email"
          class="e4s-full-width e4s-square--right"
          slot="field"
          field-type="text"
          @keyUpEnter="doRequest"
          aria-required="true"
          aria-describedby="email-description"
          data-testid="email-input"
          ref="emailInput"
        />
        <!--          <div slot="after" class="e4s-flex-row">-->
        <!--            <ButtonGenericV2-->
        <!--              text="Reset"-->
        <!--              class="e4s-square&#45;&#45;left e4s-button&#45;&#45;auto"-->
        <!--              button-type="tertiary"-->
        <!--              with-input="right"-->
        <!--              @click="doRequest"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </InputWithButton>-->
      </template>
    </FormGenericInputTemplateV2>

    <div id="email-description" class="sr-only">
      Enter your email address to receive a password reset link
    </div>

    <!--    <div-->
    <!--      v-if="message"-->
    <!--      v-text="message"-->
    <!--      class="e4s-header&#45;&#45;400"-->
    <!--      :class="{ 'e4s-body&#45;&#45;error': hasError }"-->
    <!--      data-testid="message-display"-->
    <!--    ></div>-->

    <div class="e4s-card--login-action-container">
      <a
        v-if="requestDone"
        :href="getLoginHref"
        class="e4s-hyperlink--100 e4s-hyperlink--primary"
        data-testid="back-to-login-button"
      >
        Back to Login
      </a>
      <button
        class="e4s-button e4s-button--primary"
        v-on:click="doRequest"
        v-if="!requestDone"
        data-testid="reset-button"
        :disabled="isLoading"
      >
        <span class="e4s-body--100">Reset</span>
      </button>
    </div>
    <LoadingSpinnerV2 v-if="isLoading" data-testid="loading-spinner" />
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_BASE,
} from "../../../launch/v2/launch-routes-v2";
import { ValidationService } from "../../../validation/validation-service";
import FormGenericInputTextV2 from "../../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import { CONFIG } from "../../../common/config";
import { LoginData } from "../../login-data";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { Prop } from "vue-property-decorator";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { UiVersion } from "../../../config/config-app-models";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import FormGenericInputTemplateV2 from "../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import InputWithButton from "../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FieldTextV2 from "../../../common/ui/layoutV2/fields/field-text-v2.vue";

const validationService: ValidationService = new ValidationService();

@Component({
  name: "forgot-form-v2",
  computed: {
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      uiVersion: (state: IConfigStoreState) => state.ui.version,
    }),
  },
  components: {
    LoadingSpinnerV2,
    FormGenericInputTextV2,
    ButtonGenericV2,
    FormGenericInputTemplateV2,
    InputWithButton,
    FieldTextV2,
  },
})
export default class ForgotFormV2 extends Vue {
  public readonly uiVersion!: UiVersion;

  @Prop({ default: "" }) public readonly redirectedFrom: string;

  public email: string = "";
  public message: string = "";
  public isLoading: boolean = false;
  public requestDone: boolean = false;

  public CONFIG = CONFIG;

  public doRequest() {
    this.message = "";

    if (!validationService.isEmailValid(this.email)) {
      this.message = "Valid email required.";
      return;
    }

    const loginData: LoginData = new LoginData();
    this.message = "";
    this.isLoading = true;
    loginData
      .requestPasswordReset(this.email)
      .then((response) => {
        if (response.errNo > 0) {
          this.message = response.error;
          messageDispatchHelper(
            response.error,
            USER_MESSAGE_LEVEL.ERROR.toString()
          );
          return;
        }
        // this.message =
        //   "Please follow the link sent in an email to your account.";
        this.message = response.data as any as string;
        this.requestDone = true;
      })
      .catch((error) => {
        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
        this.message = error.error;
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getEmailError() {
    return !validationService.isEmailValid(this.email)
      ? "Valid email required."
      : "";
  }

  public get getLoginHref() {
    return this.uiVersion === "v2"
      ? LAUNCH_ROUTES_PATHS_V2_BASE + "/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2
      : "#/" + LAUNCH_ROUTES_PATHS.LOGIN_V1;
  }
}
</script>
