<template>
  <div class="e4s-flex-row e4s-full-width e4s-justify-flex-center">
    <!--    <button v-on:click="onRegistered">edfwefwf</button>-->

    <RegisterFormV2 v-on:onRegistered="onRegistered" />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import RegisterFormV2 from "./register-form-v2.vue";
import { useRouter } from "../../../router/migrateRouterVue3";
import { RawLocation } from "vue-router";
import { useConfigController } from "../../../config/useConfigStore";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_BASE,
} from "../../../launch/v2/launch-routes-v2";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";

export default defineComponent({
  name: "register-container-v2",
  components: { RegisterFormV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    const router = useRouter();
    const configController = useConfigController();

    function onRegistered() {
      const loginHref =
        "/" +
        (configController.getVersion.value === "v2"
          ? LAUNCH_ROUTES_PATHS_V2_BASE + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2
          : LAUNCH_ROUTES_PATHS.LOGIN_V1);

      let location: RawLocation = {
        path: loginHref,
      };
      router.push(location);
    }

    return { onRegistered };
  },
});
</script>
