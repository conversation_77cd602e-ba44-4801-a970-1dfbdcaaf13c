<template>
  <div class="e4s-flex-row e4s-full-width e4s-justify-flex-center">
    <ResetFormV2
      v-on:onReset="onReset"
      :loginName="loginUserIdentifier"
      :resetKey="resetKey"
    />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, SetupContext } from "@vue/composition-api";
import { RawLocation, Route } from "vue-router";
import { useRoute, useRouter } from "../../../router/migrateRouterVue3";
import ResetFormV2 from "./reset-form-v2.vue";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../launch/v2/launch-routes-v2";

export default defineComponent({
  name: "reset-container-v2",
  components: { ResetFormV2 },
  props: {},
  setup(props: any, context: SetupContext) {
    const router = useRouter();
    const route: Route = useRoute();

    const loginUserIdentifier = computed(() => {
      const key = route.query["login"];
      return key ? key : "";
    });

    const resetKey = computed(() => {
      const key = route.query["key"];
      return key ? key : "";
    });

    function onReset() {
      let location: RawLocation = {
        path: "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2,
      };
      router.push(location);
    }

    return { loginUserIdentifier, resetKey, onReset };
  },
});
</script>
