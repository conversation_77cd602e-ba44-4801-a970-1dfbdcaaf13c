import * as R from "ramda";
import { IEventE4S } from "../../../event/event-models";
import { GENDER, IObjectKeyType } from "../../../common/common-models";
import { IEventGender } from "./event-selector-builder-models";
import { CommonService } from "../../../common/common-service";
import {
  EVENT_TYPE,
  EVENT_UOM_TYPE,
  IEoptions,
} from "../../../athleteCompSched/athletecompsched-models";

const commonService: CommonService = new CommonService();

export class EventSelectorBuilderService {
  public factoryEventE4S(): IEventE4S {
    return {
      id: 0,
      name: "",
      tf: EVENT_TYPE.TRACK,
      gender: "" as GENDER,
      options: {} as any as IEoptions,
      uom: {
        id: 0,
        type: EVENT_UOM_TYPE.TIME,
        options: [],
      },
    };
  }

  public factoryEventGender(): IEventGender {
    return {
      eventName: "",
      eventNameDisplay: "",
      events: {},
    };
  }

  public getEventsForDropDown(events: IEventE4S[]): IEventGender[] {
    const eventGenders = this.groupByEventName(events);
    return this.convertEventGendersForDropDown(eventGenders);
  }

  public groupByEventName(events: IEventE4S[]): IObjectKeyType<IEventGender> {
    return events.reduce((accum, evt) => {
      let eventGender: IEventGender;
      eventGender = accum[evt.name];
      if (!eventGender) {
        eventGender = {
          eventName: evt.name,
          eventNameDisplay: "",
          events: {},
        } as IEventGender;
        accum[evt.name] = eventGender;
      }

      const genderSpecificEvent = eventGender.events[evt.gender];
      if (!genderSpecificEvent) {
        eventGender.events[evt.gender] = R.clone(evt);
      }

      return accum;
    }, {} as IObjectKeyType<IEventGender>);
  }

  public convertEventGendersForDropDown(
    eventGenders: IObjectKeyType<IEventGender>
  ): IEventGender[] {
    return Object.keys(eventGenders).map((propKey: string) => {
      const eventGender = R.clone(eventGenders[propKey]);
      eventGender.eventNameDisplay = this.getDisplayName(eventGender);
      return eventGender;
    });
  }

  public getDisplayName(eventGender: IEventGender) {
    const eventsForAllGenders = commonService.convertObjectToArray(
      eventGender.events
    );
    const genderList = eventsForAllGenders.map((event) => event.gender);
    return eventGender.eventName + " (" + genderList.join(", ") + ")";
  }
}
