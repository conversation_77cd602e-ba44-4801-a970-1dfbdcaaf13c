import {IEventE4S} from "../../../event/event-models";
import {EventSelectorBuilderService} from "./event-selector-builder-service";
import {IEventGender} from "./event-selector-builder-models";

const eventSelectorBuilderService: EventSelectorBuilderService = new EventSelectorBuilderService();

describe("EventSelectorBuilder", () => {

    // @ts-ignore
    const data: IEventE4S[] = [{
        tf: "F",
        options: {
            helpText: "",
            min: 1,
            max: 10,
            isTeamEvent: false,
            excludeFromCntRule: false,
            unique: [{
                e: 30
            }],
            eventTeam: {
                min: 0,
                max: 0,
                minTargetAgeGroupCount: 0,
                maxOtherAgeGroupCount: 0,
                teamPositionLabel: "",
                maxEventTeams: 0,
                currCount: 0,
                singleClub: false,
                showForm: false,
                formType: "DEFAULT"
            },
            rowOptions: {
                autoExpandHelpText: false,
                showPB: false,
                showPrice: true,
                showEntryCount: true
            },
            maxInHeat: 0,
            class: "0,11-13,20,35-47,61-64"
        },
        id: 22,
        name: "Long Jump",
        gender: "F",
        uom: {
            id: 6,
            type: "D",
            options: [{
                pattern: 9.99,
                text: "metres",
                short: "mt"
            }]
        }
    }, {
        tf: "F",
        options: {
            helpText: "",
            min: 1,
            max: 10,
            isTeamEvent: false,
            excludeFromCntRule: false,
            unique: [{
                e: 53
            }],
            eventTeam: {
                min: 0,
                max: 0,
                minTargetAgeGroupCount: 0,
                maxOtherAgeGroupCount: 0,
                teamPositionLabel: "",
                maxEventTeams: 0,
                currCount: 0,
                singleClub: false,
                showForm: false,
                formType: "DEFAULT"
            },
            rowOptions: {
                autoExpandHelpText: false,
                showPB: false,
                showPrice: true,
                showEntryCount: true
            },
            maxInHeat: 0,
            class: "0,11-13,20,35-47,61-64"
        },
        id: 23,
        name: "Long Jump",
        gender: "M",
        uom: {
            id: 6,
            type: "D",
            options: [{
                pattern: 9.99,
                text: "metres",
                short: "mt"
            }]
        }
    }, {
        tf: "F",
        options: {
            helpText: "",
            min: 1,
            max: 9,
            isTeamEvent: false,
            excludeFromCntRule: false,
            unique: [{
                e: 22
            }],
            eventTeam: {
                min: 0,
                max: 0,
                minTargetAgeGroupCount: 0,
                maxOtherAgeGroupCount: 0,
                teamPositionLabel: "",
                maxEventTeams: 0,
                currCount: 0,
                singleClub: false,
                showForm: false,
                formType: "DEFAULT"
            },
            rowOptions: {
                autoExpandHelpText: false,
                showPB: false,
                showPrice: true,
                showEntryCount: true
            },
            maxInHeat: 0,
            class: "11-30,35-50,61-64"
        },
        id: 52,
        name: "Long Jump AMB",
        gender: "F",
        uom: {
            id: 5,
            type: "D",
            options: [{
                pattern: 0.99,
                text: "metres",
                short: "mt"
            }]
        }
    }, {
        tf: "F",
        options: {
            helpText: "",
            min: 1,
            max: 9,
            isTeamEvent: false,
            excludeFromCntRule: false,
            unique: [{
                e: 23
            }],
            eventTeam: {
                min: 0,
                max: 0,
                minTargetAgeGroupCount: 0,
                maxOtherAgeGroupCount: 0,
                teamPositionLabel: "",
                maxEventTeams: 0,
                currCount: 0,
                singleClub: false,
                showForm: false,
                formType: "DEFAULT"
            },
            rowOptions: {
                autoExpandHelpText: false,
                showPB: false,
                showPrice: true,
                showEntryCount: true
            },
            maxInHeat: 0,
            class: "11-30,35-50,61-64"
        },
        id: 53,
        name: "Long Jump AMB",
        gender: "M",
        uom: {
            id: 5,
            type: "D",
            options: [{
                pattern: 0.99,
                text: "metres",
                short: "mt"
            }]
        }
    }] as IEventE4S[];

    test("getEventsForDropDown", () => {
        const result = eventSelectorBuilderService.getEventsForDropDown(data);
        expect(result.length).toBe(2);

        const longJump = result.filter( (eventGender) => {
            return eventGender.eventName === "Long Jump";
        });
        expect(longJump.length).toBe(1);
    });

    test("getEventsForDropDown", () => {
        const eventGender = {
            eventName: "Long Jump",
            eventNameDisplay: "Long Jump (F, M)",
            events: {
                F: {
                    tf: "F",
                    options: [],
                    id: 22,
                    name: "Long Jump",
                    gender: "F",
                    uom: []
                },
                M: {
                    tf: "F",
                    options: [],
                    id: 23,
                    name: "Long Jump",
                    gender: "M",
                    uom: []
                }
            }
        } as any as IEventGender;
        expect(eventSelectorBuilderService.getDisplayName(eventGender)).toBe("Long Jump (F, M)");
    });



});
