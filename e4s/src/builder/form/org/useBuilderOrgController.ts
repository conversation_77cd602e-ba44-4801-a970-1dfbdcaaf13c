import { computed, reactive, SetupContext } from "@vue/composition-api";
import {
  BuilderOrgShowSection,
  IBuilderOrgConfig,
  IBuilderOrgState,
} from "./builder-org-models";
import {
  factoryBuilderOrgState,
  getOrganisationStatusDisplayText,
} from "./builder-org-service";
import { IOrg, IOrgWithLocations } from "../../../org/org-models";
import {
  findFirst,
  simpleClone,
  sortArray,
} from "../../../common/common-service-utils";
import { useConfigController } from "../../../config/useConfigStore";
import { OrgData } from "../../../org/org-data";
import { factoryOnboardingOrganisation } from "../../../org/onboarding/form/onboarding-service";
import { messageDispatchHelper } from "../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../user-message/user-message-models";
// import { ILocation } from "../../../location/location-models";
import { IBaseConcrete } from "../../../common/common-models";

export function useBuilderOrgController(
  config: IBuilderOrgConfig,
  context: SetupContext
) {
  const state = reactive<IBuilderOrgState>(factoryBuilderOrgState());

  const configController = useConfigController();
  const orgData = new OrgData();

  state.setUpForUser.userId =
    configController.getStore.value.configApp.userInfo.user.id;

  init();

  function init() {
    getMyOrgs().then(() => {
      if (state.orgsAvailable.length === 1) {
        onOrgSelected(state.orgsAvailable[0]);
      }
    });
  }

  function initOrgSelected(orgSelected: IOrg) {
    if (orgSelected === null) {
      return;
    }

    if (orgSelected.id === state.orgSelected.id) {
      return;
    }

    state.orgSelected = simpleClone(orgSelected);
    initOrgAvailableSelected();
  }

  function initOrgsAvailable(orgsAvailable: IOrgWithLocations[]) {
    state.orgsAvailable = simpleClone(orgsAvailable);
    initOrgAvailableSelected();
  }

  function initOrgAvailableSelected() {
    if (state.orgSelected.id === 0) {
      return;
    }

    if (state.orgsAvailable.length === 0) {
      return;
    }

    // Sort orgs by name we might have scenario where org selected is not in user list!!   E.g. E4S Admin
    if (state.orgSelected.id > 0) {
      const findOrgInAvailable: IBaseConcrete | null = findFirst((org) => {
        return org.id === state.orgSelected.id;
      }, state.orgsAvailable);
      if (findOrgInAvailable === null) {
        let orgs = simpleClone(state.orgsAvailable);
        // orgs.push({ id: state.orgSelected.id, name: state.orgSelected.name });
        orgs.push(simpleClone({ ...state.orgSelected, locations: {} }));

        orgs = sortArray("name", orgs);
        state.orgsAvailable = orgs;
      }
    }

    // Loop though state.orgsAvailable and set state.orgsAvailableSelected where the id matches state.orgSelected.id
    for (let i = 0; i < state.orgsAvailable.length; i++) {
      if (state.orgsAvailable[i].id === state.orgSelected.id) {
        state.orgsAvailableSelected = simpleClone(state.orgsAvailable[i]);
        break;
      }
    }
  }

  function onOrgSelected(org: IOrg) {
    console.log("useBuilderOrgController onOrgSelected", org);
    state.orgSelected = simpleClone(org);
    state.orgsAvailableSelected = simpleClone(org) as IOrgWithLocations;
    emitOrgSelected();
    // getOrgLocations(org);
  }

  function onOrgSelectedAndClose(org: IOrg) {
    onOrgSelected(org);
    cancelCreateNewOrg();
  }

  // function getOrgLocations(org: IOrg) {
  //   alert("DEPRECATED");
  //   state.isLoading = true;
  //   orgData
  //     .list({
  //       startswith: org.name,
  //       pagenumber: 1,
  //       pagesize: 5,
  //       sortkey: "name",
  //     })
  //     .then((response) => {
  //       const orgsWithLocations =
  //         response.data as unknown as IOrgWithLocations[];
  //
  //       if (orgsWithLocations.length === 1) {
  //         const locations: ILocation[] = Object.values(
  //           orgsWithLocations[0].locations
  //         );
  //         state.orgSelectedLocations = locations;
  //         // emitOrgSelectedLocations();
  //         context.emit("orgSelectedLocations", state.orgSelectedLocations);
  //       }
  //
  //       console.log(
  //         "useBuilderOrgController getOrgLocations",
  //         orgsWithLocations
  //       );
  //     })
  //     .finally(() => {
  //       state.isLoading = false;
  //     });
  // }

  function emitOrgSelected() {
    console.log("useBuilderOrgController emitOrgSelected", state.orgSelected);
    context.emit("orgSelected", state.orgSelected);
  }

  // function emitOrgSelectedLocations() {
  //   context.emit("orgSelectedLocations", state.orgSelectedLocations);
  // }

  function showCreateNewOrg() {
    state.onboardingOrganisation = factoryOnboardingOrganisation();
    // state.showSection = "create-new-org";
    // context.emit("orgShowSection", state.showSection);
    setShowSection("create-new-org");
  }

  function submitNewOrg(org: IOrg) {
    state.orgSelected = simpleClone(org);

    emitOrgSelected();
    getMyOrgs()
      .then(() => {
        initOrgAvailableSelected();
        // state.showSection = "select-org";
        // setShowSection("select-org");
      })
      .finally(() => {
        cancelCreateNewOrg();
      });
  }

  function cancelCreateNewOrg() {
    // state.showSection = "select-org";
    setShowSection("select-org");
    context.emit("cancel");
  }

  function setShowSection(section: BuilderOrgShowSection) {
    state.showSection = section;
    context.emit("orgShowSection", state.showSection);
  }

  function getMyOrgs(): Promise<void> {
    return orgData.getMyOrgs(state.setUpForUser.userId).then((response) => {
      if (response.errNo > 0) {
        messageDispatchHelper(
          response.error,
          USER_MESSAGE_LEVEL.ERROR.toString()
        );
        return;
      }

      state.orgsAvailable = response.data;
      initOrgAvailableSelected();
    });
  }

  const isStripeSetupRequired = computed(() => {
    if (state.orgSelected.id === 0) {
      return false;
    }

    if (!configController.getStore.value.configApp.options.useStripeConnect) {
      return false;
    }

    if (state.orgSelected.stripeUser === null) {
      return true;
    }

    return state.orgSelected.stripeUser.id === 0;
  });

  const getOrganisationTitle = computed(() => {
    let defaultTitle = "Organisation Name";
    if (state.orgSelected.id > 0) {
      defaultTitle +=
        " - Status: (" +
        getOrganisationStatusDisplayText(state.orgSelected.status) +
        ")";
    }

    return defaultTitle;
  });

  const showRequiresApprovalMessage = computed(() => {
    if (state.orgSelected.id === 0) {
      return false;
    }
    return state.orgSelected.status !== "Approved";
  });

  return {
    state,
    initOrgSelected,
    initOrgsAvailable,
    configController,
    onOrgSelected,
    onOrgSelectedAndClose,
    showCreateNewOrg,
    submitNewOrg,
    cancelCreateNewOrg,
    isStripeSetupRequired,
    getOrganisationTitle,
    showRequiresApprovalMessage,
    setShowSection,
  };
}
