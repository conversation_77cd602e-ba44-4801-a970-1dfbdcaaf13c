import * as SubscriptionService from "./subscription-service";
import {IBuilderCompetition} from "../../builder-models";

describe("SubscriptionService", () => {
  test("validateSubscription", () => {
    let result;

    const builderCompetition = {
      date: "2022-12-12",
      entriesOpenDateTime: "2022-11-10T09:00:00+01:00",
      entriesCloseDateTime: "2022-12-10T19:00:00+01:00"
    } as IBuilderCompetition

    const data = {
      enabled: true,
      timeCloses: "2022-11-28T19:00:00+00:00",
      organiserMessage: "org waiting message",
      e4sMessage: "",
      process: false,
      refunded: "",
      processRefundTime: "",  //2022-12-12T19:00:00+01:00
    };
    // const compDate = "2022-12-12";

    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.processRefundTime.messages.length > 0).toBe(true);


    data.processRefundTime = "2022-12-01T19:00:00+01:00";
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.processRefundTime.messages.length > 0).toBe(true);

    data.processRefundTime = "2022-12-12T19:00:00+01:00";
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.processRefundTime).toBe(undefined);

    data.processRefundTime = "2022-12-12";
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.processRefundTime).toBe(undefined);

    data.timeCloses = "2022-12-10T10:00:00+01:00"
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.timeCloses).toBe(undefined);


    data.timeCloses = "2022-10-10T10:00:00+01:00"
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.timeCloses.messages.length > 0).toBe(true);

    data.timeCloses = "2022-12-30T10:00:00+01:00"
    result = SubscriptionService.validateSubscription(data, builderCompetition)
    expect(result.timeCloses.messages.length > 0).toBe(true);

  });
});
