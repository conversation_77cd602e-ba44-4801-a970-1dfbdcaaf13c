import { mapTimeOptionsToRequest } from "../time-options-mapper";
import { ITimeOption } from "../../models/time-options-api-models";
import { groupCompEventsByDate } from "../time-options-mapper";
import { ICompEvent } from "../../../../../compevent/compevent-models";

describe("time-options-mapper", () => {
  test("mapTimeOptionsToRequest should correctly map array to request object", () => {
    const timeOptions: ITimeOption[] = [
      {
        eventDate: "2025-05-01",
        availableFrom: "2025-03-01 00:00",
        availableFromStatus: -1,
        availableTo: "2025-04-01 00:00",
        availableToStatus: 0,
      },
      {
        eventDate: "2025-06-01",
        availableFrom: "2025-04-01 00:00",
        availableFromStatus: -1,
        availableTo: "2025-05-01 00:00",
        availableToStatus: 0,
      },
    ];

    const result = mapTimeOptionsToRequest(314, timeOptions);

    expect(result).toEqual({
      compid: 314,
      dates: {
        "2025-05-01": {
          availableFrom: "2025-03-01 00:00",
          availableFromStatus: -1,
          availableTo: "2025-04-01 00:00",
          availableToStatus: 0,
        },
        "2025-06-01": {
          availableFrom: "2025-04-01 00:00",
          availableFromStatus: -1,
          availableTo: "2025-05-01 00:00",
          availableToStatus: 0,
        },
      },
    });
  });

  test("mapTimeOptionsToRequest should handle empty array", () => {
    const result = mapTimeOptionsToRequest(314, []);

    expect(result).toEqual({
      compid: 314,
      dates: {},
    });
  });

  test("mapTimeOptionsToRequest should handle single item", () => {
    const timeOptions: ITimeOption[] = [
      {
        eventDate: "2025-05-01",
        availableFrom: "2025-03-01 00:00",
        availableFromStatus: -1,
        availableTo: "2025-04-01 00:00",
        availableToStatus: 0,
      },
    ];

    const result = mapTimeOptionsToRequest(314, timeOptions);

    expect(result).toEqual({
      compid: 314,
      dates: {
        "2025-05-01": {
          availableFrom: "2025-03-01 00:00",
          availableFromStatus: -1,
          availableTo: "2025-04-01 00:00",
          availableToStatus: 0,
        },
      },
    });
  });
});

describe("groupCompEventsByDate", () => {
  test("should group comp events with availability options", () => {
    const compEvents: ICompEvent[] = [
      {
        id: 1,
        startDateTime: "2025-05-01T09:00:00",
        options: {
          availableFrom: "2025-04-01T00:00:00",
          availableFromStatus: -1,
          availableTo: "2025-05-01T08:00:00",
          availableToStatus: 0,
        },
      } as ICompEvent,
      {
        id: 2,
        startDateTime: "2025-05-01T14:00:00",
        options: {
          availableFrom: "2025-04-01T00:00:00", // Same as previous event
          availableFromStatus: -1,
        },
      } as ICompEvent,
      {
        id: 3,
        startDateTime: "2025-05-02T10:00:00",
        // No availability options
      } as ICompEvent,
    ];

    const result = groupCompEventsByDate(compEvents);

    expect(Object.keys(result)).toHaveLength(2);

    // First date should have 2 unique time options
    expect(result["2025-05-01"]).toHaveLength(3);
    expect(result["2025-05-01"]).toContainEqual({
      eventDate: "2025-05-01",
      availableFrom: "2025-04-01T00:00:00",
      availableTo: "2025-05-01T09:00:00",
      availableFromStatus: -1,
      availableToStatus: 0,
    });
    expect(result["2025-05-01"]).toContainEqual({
      eventDate: "2025-05-01",
      availableFrom: "2025-05-01T09:00:00",
      availableTo: "2025-05-01T08:00:00",
      availableFromStatus: 0,
      availableToStatus: 0,
    });

    // Second date should have default time option
    expect(result["2025-05-02"]).toHaveLength(1);
    expect(result["2025-05-02"][0]).toEqual({
      eventDate: "2025-05-02",
      availableFrom: "2025-05-02T10:00:00",
      availableTo: "2025-05-02T10:00:00",
      availableFromStatus: 0,
      availableToStatus: 0,
    });
  });

  test("should not duplicate identical time options", () => {
    const compEvents: ICompEvent[] = [
      {
        id: 1,
        startDateTime: "2025-05-01T09:00:00",
        options: {
          availableFrom: "2025-04-01T00:00:00",
          availableFromStatus: -1,
        },
      } as ICompEvent,
      {
        id: 2,
        startDateTime: "2025-05-01T14:00:00",
        options: {
          availableFrom: "2025-04-01T00:00:00",
          availableFromStatus: -1,
        },
      } as ICompEvent,
    ];

    const result = groupCompEventsByDate(compEvents);
    expect(result["2025-05-01"]).toHaveLength(2); // Only unique time options
  });

  test("should handle empty array", () => {
    const result = groupCompEventsByDate([]);
    expect(result).toEqual({});
  });
});
