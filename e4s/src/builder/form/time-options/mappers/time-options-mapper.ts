import {
  ITimeOptionsRequest,
  ITimeOptionAvailability,
  ITimeOption,
} from "../models/time-options-api-models";
import { ICompEvent } from "../../../../compevent/compevent-models";
import { IsoDate } from "../../../../common/common-models";

function findExistingTimeOption(
  timeOptions: ITimeOption[],
  newOption: ITimeOption
): boolean {
  return timeOptions.some(
    (option) =>
      option.availableFrom === newOption.availableFrom &&
      option.availableTo === newOption.availableTo &&
      option.availableFromStatus === newOption.availableFromStatus &&
      option.availableToStatus === newOption.availableToStatus
  );
}

export function mapTimeOptionsToRequest(
  compid: number,
  timeOptions: ITimeOption[]
): ITimeOptionsRequest {
  const dates = timeOptions.reduce<Record<string, ITimeOptionAvailability>>(
    (acc, option) => {
      acc[option.eventDate] = {
        availableFrom: option.availableFrom,
        availableFromStatus: option.availableFromStatus,
        availableTo: option.availableTo,
        availableToStatus: option.availableToStatus,
      };
      return acc;
    },
    {}
  );

  return {
    compid,
    dates,
  };
}

export function groupCompEventsByDate(
  compEvents: ICompEvent[]
): Record<IsoDate, ITimeOption[]> {
  return compEvents.reduce<Record<IsoDate, ITimeOption[]>>((acc, compEvent) => {
    const eventDate = compEvent.startDateTime.split("T")[0];

    if (!acc[eventDate]) {
      acc[eventDate] = [];
    }

    // Check for availableFrom in options
    if (compEvent.options?.availableFrom) {
      const fromOption: ITimeOption = {
        eventDate,
        availableFrom: compEvent.options.availableFrom,
        availableTo: compEvent.startDateTime,
        availableFromStatus: compEvent.options.availableFromStatus || 0,
        availableToStatus: 0,
      };

      if (!findExistingTimeOption(acc[eventDate], fromOption)) {
        acc[eventDate].push(fromOption);
      }
    }

    // Check for availableTo in options
    if (compEvent.options?.availableTo) {
      const toOption: ITimeOption = {
        eventDate,
        availableFrom: compEvent.startDateTime,
        availableTo: compEvent.options.availableTo,
        availableFromStatus: 0,
        availableToStatus: compEvent.options.availableToStatus || 0,
      };

      if (!findExistingTimeOption(acc[eventDate], toOption)) {
        acc[eventDate].push(toOption);
      }
    }

    // If no specific availability options, use default
    if (!compEvent.options?.availableFrom && !compEvent.options?.availableTo) {
      const defaultOption: ITimeOption = {
        eventDate,
        availableFrom: compEvent.startDateTime,
        availableTo: compEvent.startDateTime,
        availableFromStatus: 0,
        availableToStatus: 0,
      };

      if (!findExistingTimeOption(acc[eventDate], defaultOption)) {
        acc[eventDate].push(defaultOption);
      }
    }

    return acc;
  }, {});
}
