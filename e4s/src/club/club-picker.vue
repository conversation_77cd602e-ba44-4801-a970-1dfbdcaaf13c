<template>
    <div class="row">
        <div class="col s8 m8 l8">
            <ClubSearch v-if="showClubAdd"
                         :field-label="isSchoolSearch ? 'School' : 'Club'"
                         :is-school="isSchoolSearch"
                         v-on:onSelected="onClubSelected">
            </ClubSearch>
            <span v-if="!showClubAdd" v-text="clubToAdd.clubName"></span>
        </div>
        <div class="col s4 m4 l4">
            <div class="right">
                <loading-spinner v-if="isLoading"></loading-spinner>

                <div class="e4s-force-inline-block">
                    <p>
                        <label>
                            <input id="is-school-search"
                                   class="e4s-checkbox"
                                   type="checkbox"
                                   v-model="isSchoolSearch" />
                            <span>
                                    School
                                </span>
                        </label>
                    </p>
                </div>

                <button :disabled="isLoading"
                        v-if="clubToAdd.id > 0"
                        class="btn waves-effect waves red button-width"
                        v-on:click.stop="cancelClub">
                    <span v-text="$t('buttons.cancel')"></span>
                </button>
                <slot name="addClubButton">
                    <button :disabled="isLoading"
                            v-if="clubToAdd.id === 0"
                            class="btn waves-effect waves green button-width"
                            v-on:click.stop="addClub">
                        <span v-text="$t('buttons.add')"></span>
                    </button>
                </slot>
                <slot name="submitClubButton">
                    <button slot="submitClubButton"
                            :disabled="isLoading"
                            v-if="clubToAdd.id > 0"
                            class="btn waves-effect waves green button-width"
                            v-on:click.stop="submitClub">
                        <span v-text="$t('buttons.save')"></span>
                    </button>
                </slot>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IClubLookup} from "./club-models";
    import * as R from "ramda";
    import {ClubService} from "./club-service";
    import ClubSearch from "./club-search.vue";

    const clubService: ClubService = new ClubService();

    @Component({
        name: "club-picker",
        components: {
            ClubSearch

        }
    })
    export default class ClubPicker extends Vue {
        public isLoading: boolean = false;
        public clubToAdd: IClubLookup = clubService.factoryIClubLookup();
        public showClubAdd: boolean = true;
        public isSchoolSearch: boolean = false;

        public onClubSelected(club: IClubLookup) {
            this.clubToAdd = R.clone(club);
            this.showClubAdd = false;
            this.$emit("onClubSelected", Object.assign({}, club));
        }


        public cancelClub() {
            this.clubToAdd = clubService.factoryIClubLookup();
            this.$emit("onClubCancel");
        }

        public addClub() {
            console.log("User.addClub()");
            this.showClubAdd = true;
        }

    }
</script>
