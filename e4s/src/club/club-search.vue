<template>
    <auto-complete-mat
            :field-label="fieldLabel"
            :custom="getCustomForAutoComplete"
            :data = "clubItems"
            iconClassName=""
            :placeholder="placeHolder"
            :is-loading="isLoading"
            :user-input-preload="getSelectedName"
            v-on:searchTermChanged="onSearchClub"
            v-on:autoSelectionMade="onSelectClub">
    </auto-complete-mat>
</template>

<script lang="ts">
    import * as R from "ramda";
    import Vue from "vue";
    import Component from "vue-class-component";
    import {IClub, IClubLookup} from "./club-models";
    import {IBase, IServerResponse} from "../common/common-models";
    import {ClubData} from "./club-data";
    import {USER_MESSAGE_LEVEL} from "../user-message/user-message-models";
    import {messageDispatchHelper} from "../user-message/user-message-store";
    import AutoCompleteMat from "../common/ui/autocomplete/auto-complete-mat.vue";
    import { Prop, Watch } from "vue-property-decorator";
    import {IAutoCompleteValue, ICustom} from "../common/ui/autocomplete/auto-complete-mat-models";
    import { ClubService } from "./club-service";

    const clubService: ClubService = new ClubService();

    @Component({
        name: "club-search",
        components: {
            AutoCompleteMat
        }
    })
    export default class ClubSearch extends Vue {

        @Prop({
            default: () => {
                return clubService.factoryIClub();
            }
        }) public readonly club: IClub;
        @Prop({default: "Club"}) public readonly fieldLabel: string;
        @Prop({default: "Enter search term..."}) public readonly placeHolder: string;
        @Prop({default: false}) public readonly isSchool: boolean;
        @Prop({default: true}) public readonly autoAllReturn: boolean;


        public selectedClub: IClubLookup = clubService.factoryIClubLookup();
        public clubItems: IClubLookup[] = [];
        public isLoading: boolean = false;
        public noClubData: boolean = false;
        public selectClubDebounceId: any;
        public clubData: ClubData = new ClubData();

        @Watch("club")
        public onClubChanged(club: IClub) {
            this.selectedClub = clubService.convertClubToClubLookup(club);
        }

        public onSearchClub(key: string) {

            if (key.length < 1 ) {
                return;
            }
            if (this.autoAllReturn) {
                if (key.toUpperCase() === "ALL") {
                    this.$emit("onSelected", {
                        id: 0,
                        name: key.toUpperCase()
                    } as IBase);
                    return;
                }
            }

            this.isLoading = true;
            clearTimeout(this.selectClubDebounceId);

            this.selectClubDebounceId = window.setTimeout( () => {
                const areaId = 0;
                return this.clubData.searchClubs(areaId, key, this.isSchool)
                    .then( (response: IServerResponse<IClubLookup[]>) => {
                        if (response.errNo > 0) {
                            messageDispatchHelper(response.error, USER_MESSAGE_LEVEL.ERROR.toString());
                            return;
                        }
                        const clubItems = response.data.filter((club) => club.id > 0);
                        this.clubItems = clubItems;
                        this.noClubData = response.data.length === 0;
                        return;
                    })
                    .catch((error) => {
                        messageDispatchHelper(error, USER_MESSAGE_LEVEL.ERROR.toString());
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            }, 100);
        }

        public get getCustomForAutoComplete(): ICustom {
            return {
                dropDownLabelFunc: this.getDropDownClubDescription
            } as ICustom;
        }

        public getDropDownClubDescription(clubLookup: IClubLookup): string {
            if (clubLookup.id === 0) {
                return "";
            }
            return clubLookup.clubName + " - " + clubLookup.county + " - " + clubLookup.region;
        }

        public get getSelectedName() {
            return this.getDropDownClubDescription(this.selectedClub);
        }

        // public onSelectClub(club: IClubLookup) {
        //     this.$emit("onSelected", R.clone(club));
        // }

        public onSelectClub(autoCompleteValue: IAutoCompleteValue) {
            // console.log("CompEventNamePicker.athleteSelected", autoCompleteValue);
            this.$emit("onSelected", R.clone(autoCompleteValue.value));
        }
    }
</script>
