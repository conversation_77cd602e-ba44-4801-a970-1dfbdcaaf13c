<template>
    <div class="row">
        <div class="col s12">
            <div class="row">
                <div class="input-field col s12">
                    <i class="material-icons prefix">textsms</i>
                    <input type="text" id="autocomplete-input" class="autocomplete">
                    <label for="autocomplete-input">Autocomplete</label>
                </div>
            </div>
        </div>
        dropDownData{{dropDownData}}
    </div>
</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {IObjectKey} from "../../common-models";
    import * as R from "ramda";

    @Component({
        name: "auto-complete-test"
    })
    export default class AutoCompleteTest extends Vue {

        @Prop({default: () => []}) public readonly data: any[];                  //  Default data
        @Prop({default: null}) public labelProp: string;                //  default prop from object to display in drop down

        public instance: any;
        public dropDownData: IObjectKey = {};

        public created() {
            // console.log("AutoCompleteMatSimple.created() data tttttttttttttttttttttttttttttt", this.data);
            this.setData(this.data);
        }

        public mounted() {
            // const elems = document.querySelectorAll(".autocomplete");
            const elems = document.getElementById("autocomplete-input");

            // this.dropDownData = {
            //     one: null,
            //     two: null,
            //     three: null,
            //     four: null
            // };

            const options = {
                data: R.clone(this.dropDownData)
            };

            // const options = {
            //     data: this.dropDownData
            // };
            this.instance = (window as any).M.Autocomplete.init(elems, options);
            // this.instance =
            // const instance = instances[0];
            // instance.open();


        }

        @Watch("data")
        public onDatachanged(newValue: any[]) {
            this.setData(newValue);
            if (this.instance && this.instance.updateData) {
                this.instance.updateData(R.clone(this.dropDownData));
            }
        }

        public setData(data: any[]): void {
            if (R.isNil(data)) {
                // console.log("AutoCompleteMat.data is nil????");
            }

            if (data && data.length > 0) {
                const dropDownData = this.getDataObject(data, false);
                this.dropDownData = dropDownData;
            }
        }

        public getDataObject = (events: any[], addObject: boolean): any => {
            const dataObj = events.reduce((accum: any, obj: any) => {
                const keyUserEventUnique: string = this.getObjectKeyValue(obj);
                if (!accum[keyUserEventUnique]) {
                    //  TODO totally annoying, how store object and not have the image get loaded???
                    //   @see https://materializecss.com/autocomplete.html
                    accum[keyUserEventUnique] = addObject ? obj : null;
                }
                return accum;
            }, {});

            return dataObj;
        }

        public getObjectKeyValue(obj: any): string {
            if (!obj) {
                return "NOT_FOUND";
            }
            return obj[this.labelProp];
        }
    }
</script>
