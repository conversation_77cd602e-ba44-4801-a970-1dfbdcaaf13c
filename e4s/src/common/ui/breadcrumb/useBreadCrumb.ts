import {
  BreadCrumb,
  BreadCrumbBase,
  breadCrumbName,
} from "./breadcrumb-models";
import { reactive } from "@vue/composition-api";
import { simpleClone } from "../../common-service-utils";
import * as BreadcrumbService from "./breadcrumb-service";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../launch/v2/launch-routes-v2";
import { RawLocation } from "vue-router";
import { ENTRY_STORE_CONST } from "../../../entry/entry-store";
import { useRoute, useRouter } from "../../../router/migrateRouterVue3";
import { useStore } from "../../../app.store";
import { useEntryNavigationHelper } from "../../../entry/v2/useEntry";

export interface BreadCrumbControllerState {
  breadCrumbsMap: Record<breadCrumbName, BreadCrumb>;
}

const globalState = reactive<BreadCrumbControllerState>(
  BreadcrumbService.factoryBreadCrumbControllerState()
);

export interface BreadCrumbControllerConfig {
  useGlobalState: boolean;
  compId: number;
}

export function useBreadCrumbController(
  breadCrumbControllerConfig: BreadCrumbControllerConfig
) {
  const state = reactive<BreadCrumbControllerState>(
    breadCrumbControllerConfig.useGlobalState
      ? globalState
      : BreadcrumbService.factoryBreadCrumbControllerState()
  );

  const store = useStore();
  const routeInternal = useRoute();
  const routerInternal = useRouter();

  const entryNavigationHelper = useEntryNavigationHelper();

  function setBreadCrumb(breadCrumb: BreadCrumb) {
    state.breadCrumbsMap[breadCrumb.name] = simpleClone(breadCrumb);
  }

  function setLastBreadCrumb(breadCrumb: BreadCrumbBase) {
    const initialBreadCrumbs: Record<breadCrumbName, BreadCrumb> =
      BreadcrumbService.factoryBreadCrumbControllerState().breadCrumbsMap;

    //  Set up the last breadcrumb
    initialBreadCrumbs[breadCrumb.name].isVisible = true;
    initialBreadCrumbs[breadCrumb.name].isLast = true;
    initialBreadCrumbs[breadCrumb.name].isClickable = false;
    if (breadCrumb.title) {
      initialBreadCrumbs[breadCrumb.name].title = breadCrumb.title;
    }

    if (breadCrumb.name === "COMP_HOME") {
      initialBreadCrumbs.COMP_HOME.isVisible = true;
    }

    if (breadCrumb.name === "COMP_ATHLETES") {
      initialBreadCrumbs.COMP_HOME.isVisible = true;
    }

    if (breadCrumb.name === "COMP_ATHLETE") {
      initialBreadCrumbs.COMP_HOME.isVisible = true;
      initialBreadCrumbs.COMP_ATHLETES.isVisible = true;
    }

    state.breadCrumbsMap = initialBreadCrumbs;
  }

  function reset() {
    Object.assign(state, BreadcrumbService.factoryBreadCrumbControllerState());
  }

  function goHome() {
    routerInternal
      .push({
        name: LAUNCH_ROUTES_PATHS_V2.HOME_V2,
      } as RawLocation)
      .catch((error: any) => {
        console.log("useBreadCrumbController goHome error");
        store.commit(
          ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME +
            "/" +
            ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_SET_ROUTE_ERROR,
          error
        );
      });
  }

  function goCompHome() {
    if (routeInternal.name === LAUNCH_ROUTES_PATHS_V2.SHOW_ENTRY_v2) {
      console.log(
        "useBreadCrumbController goCompHome already on: " +
          LAUNCH_ROUTES_PATHS_V2.SHOW_ENTRY_v2
      );
    }

    routerInternal.push({
      name: LAUNCH_ROUTES_PATHS_V2.SHOW_ENTRY_v2,
      params: {
        id: breadCrumbControllerConfig.compId.toString(),
      },
    });
  }

  function goToAthletes() {
    setLastBreadCrumb({
      name: "COMP_ATHLETES",
      title: "Athletes",
    });

    // const sectionLink =
    //   entryNavigationHelper.state.ui.compSectionLinkMap.ATHLETES_GRID;
    entryNavigationHelper.onLinkSelected({
      iconId: "1a",
      title: "Athletes",
      uniqueDesc: "ATHLETES_GRID",
    });
  }

  return {
    state,
    setLastBreadCrumb,
    setBreadCrumb,
    reset,
    goHome,
    goCompHome,
    goToAthletes,
    entryNavigationHelper,
  };
}
