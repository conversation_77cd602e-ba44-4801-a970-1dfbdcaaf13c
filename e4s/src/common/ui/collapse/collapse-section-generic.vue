<template>
    <div>
        <div v-show="getShowHeader">
            <slot name="header-content-use-supplied-click">
                <div v-on:click.prevent="isExpandedInternal = true">
                    <slot name="header-content">
                        * * * This is where you place your header content. * * *
                    </slot>
                    <a href="#" v-on:click.prevent="isExpandedInternal = true">More Info...</a>
                </div>
            </slot>
        </div>

        <div v-show="isExpandedInternal">
            <slot name="section-content-use-supplied-click">
                <div v-on:click.prevent="onSectionClicked">
                    <a href="#" v-on:click.prevent="isExpandedInternal = false">Close</a>
                    <slot name="section-content">
                        * * * This is where you place your section content. * * *
                    </slot>
                </div>
            </slot>
        </div>
    </div>


</template>

<script lang="ts">

    import Vue from "vue";
    import Component from "vue-class-component";
    import {Prop, Watch} from "vue-property-decorator";
    import {IConfigApp} from "../../../config/config-app-models"

    @Component({
        name: "collapse-section-generic"
    })
    export default class CollapseSectionGeneric extends Vue {
        public readonly configApp: IConfigApp;

        @Prop({default: false}) public readonly isExpanded: boolean;
        @Prop({default: true}) public readonly allowExpandCollapse: boolean;
        @Prop({default: false}) public readonly showHeaderOnExpand: boolean;

        public isExpandedInternal: boolean = false;

        public created() {
            this.isExpandedInternal = this.isExpanded;
        }

        @Watch("isExpanded")
        public isExpandedChanged(newValue: boolean) {
            this.isExpandedInternal = newValue;
        }

        public onSectionClicked() {
            return;
            // if (this.allowExpandCollapse) {
            //     this.isExpandedInternal = !this.isExpandedInternal;
            // }
        }

        public get getShowHeader() {
            if (this.showHeaderOnExpand) {
                //  As in...always show it.
                return true;
            }
            return !this.isExpandedInternal;
        }

    }
</script>

<style scoped>
    .e4s-collapsible-body {
        padding: 1rem;
    }
</style>
