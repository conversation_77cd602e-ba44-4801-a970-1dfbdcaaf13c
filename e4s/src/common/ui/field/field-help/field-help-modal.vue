<template>
    <e4s-modal v-if="false">
<!--<e4s-modal v-if="fieldHelpStoreState.showHelp">-->
        <div slot="header">
            <div>
                <span v-text="fieldHelpStoreState.helpData.title"></span>
            </div>
        </div>

        <div slot="body">

            <div class="e4s-modal-body">
                <div class="row">
                    <div class="col s12 m12 l12">
                        <div v-html="getMessage"
                             v-if="fieldHelpStoreState.helpData.type !== 'U'">
                        </div>
                        <div v-if="fieldHelpStoreState.helpData.type === 'U'">
                            <iframe :src="fieldHelpStoreState.helpData.data"></iframe>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div slot="footer">
            <div class="e4s-modal-footer">
                <div class="row">
                    <div class="col s12 m12 l12">
                        <div class="right">

                            <slot name="button-close-primary">
                                <div v-if="fieldHelpStoreState.isLoading" class="e4s-force-inline-block">
                                    <loading-spinner></loading-spinner>
                                </div>
                                <button class="btn btn-flat green-text e4s-bold"
                                        v-on:click.stop="hideHelp">
                                    <span>OK</span>
                                </button>
                            </slot>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </e4s-modal>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import {FIELD_HELP_STORE_CONST, IFieldHelpStoreState} from "./field-help-store";
    import {mapState} from "vuex";

    const E4sModal = () => {
        // import E4sModal from "../../e4s-modal.vue";
        return import(/* webpackChunkName: "dyn-field-help-modal" */ "../../e4s-modal.vue");
    };

    @Component({
        name: "field-help-modal",
        computed: {
            ...mapState(FIELD_HELP_STORE_CONST.CONST_MODULE_NAME, {
                fieldHelpStoreState: (state: IFieldHelpStoreState) => state
            }),
        },
        components: {
            E4sModal
        }
    })
    export default class FieldHelpModal extends Vue {
        public readonly fieldHelpStoreState: IFieldHelpStoreState;

        public get getMessage() {
            return this.fieldHelpStoreState.helpData.data;
        }

        public hideHelp() {
            this.$store.commit(
                FIELD_HELP_STORE_CONST.CONST_MODULE_NAME + "/" +
                FIELD_HELP_STORE_CONST.MUTATIONS_SET_SHOW_HELP,
                false
            );
        }
    }
</script>
