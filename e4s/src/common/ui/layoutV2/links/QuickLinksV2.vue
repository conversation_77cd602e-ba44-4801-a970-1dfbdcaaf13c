<template>
  <InputWithButton>
    <FieldSelectV2
      slot="field"
      :data-array="getLinks"
      v-on:input="selectedLink = $event"
      class="e4s-full-width e4s-square--right"
    />
    <div slot="after" class="e4s-flex-row">
      <ButtonGenericV2
        class="e4s-square--left e4s-button--auto"
        text="Go"
        v-on:click="goToLink"
      />
    </div>
  </InputWithButton>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import { CONFIG } from "../../../config";
import InputWithButton from "../fields/InputWithButton.vue";
import ButtonGenericV2 from "../buttons/button-generic-v2.vue";
import FieldSelectV2 from "../fields/field-select-v2.vue";
import { IPublicListCompCardLink } from "../../../../public/entry-public/public-list/v2/public-list-comp-card-models";
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import { sortArray } from "../../../common-service-utils";
import { RawLocation } from "vue-router";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { useCompPermissions } from "../../../../config/useCompPermissions";
import { useRouter } from "../../../../router/migrateRouterVue3";
import {
  LAUNCH_ROUTES_PATHS_V2,
  LAUNCH_ROUTES_PATHS_V2_DIR,
} from "../../../../launch/v2/launch-routes-v2";

export default defineComponent({
  name: "quick-links-v2",
  components: { FieldSelectV2, ButtonGenericV2, InputWithButton },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    const compPermissions = useCompPermissions(props.competitionSummaryPublic);
    const router = useRouter();

    const selectedLink = ref<IPublicListCompCardLink>({
      id: 0,
      name: "",
      link: "",
      targetName: "",
    });

    const getLinks = computed<IPublicListCompCardLink[]>(() => {
      const links: IPublicListCompCardLink[] = [];

      links.push({
        id: links.length,
        name: "Schedule",
        link:
          CONFIG.E4S_HOST +
          "/entry/v5/competition/schedule.php?compid=" +
          props.competitionSummaryPublic.compId,
        targetName: "_schedule",
      });

      if (
        props.competitionSummaryPublic.link &&
        props.competitionSummaryPublic.link.length > 0
      ) {
        links.push({
          id: links.length,
          name: "Flyer",
          link: props.competitionSummaryPublic.link,
          targetName: "_flyer",
        });
      }

      links.push({
        id: links.length,
        name: "Seeding",
        link:
          CONFIG.E4S_HOST +
          "/wp-json/e4s/v5/public/reports/cards/" +
          props.competitionSummaryPublic.compId,
        targetName: "_seeding",
      });

      if (compPermissions.hasBuilderPermissionForComp.value) {
        // links.push({
        //   id: links.length,
        //   name: "---------",
        //   link: "SPACER_LINE",
        //   disabled: true
        // });

        links.push({
          id: links.length,
          name: "Bibs",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.BIBS +
            "/" +
            props.competitionSummaryPublic.compId,
        });

        links.push({
          id: links.length,
          name: "Builder",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS_V2_DIR +
            "/" +
            LAUNCH_ROUTES_PATHS_V2.BUILDER_V2 +
            "/" +
            props.competitionSummaryPublic.compId,
        });

        links.push({
          id: links.length,
          name: "Email\\Message",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS_V2_DIR +
            "/" +
            LAUNCH_ROUTES_PATHS_V2.MESSAGES_V2 +
            "/" +
            props.competitionSummaryPublic.compId,
        });

        links.push({
          id: links.length,
          name: "Organiser Report",
          link:
            CONFIG.E4S_HOST + props.competitionSummaryPublic.compId + "/report",
          targetName: "_organiser-report",
        });
      }

      if (compPermissions.hasTicketPermissionForComp.value) {
        links.push({
          id: links.length,
          name: "Tickets (Scan)",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.TICKET_FORM_GATEKEEPER +
            "/" +
            props.competitionSummaryPublic.compId,
        });

        links.push({
          id: links.length,
          name: "Tickets (Admin)",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.TICKET_ADMIN +
            "/" +
            props.competitionSummaryPublic.compId,
        });
      }

      if (compPermissions.hasCheckinPermissionForComp.value) {
        // links.push({
        //   id: links.length,
        //   name: "---------",
        //   link: "SPACER_LINE",
        //   disabled: true
        // });

        links.push({
          id: links.length,
          name: "Check-in",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.CHECKIN_ORGANISER +
            "/" +
            props.competitionSummaryPublic.compId,
        });

        links.push({
          id: links.length,
          name: "Result Entry",
          link:
            CONFIG.E4S_HOST +
            "/wp-json/e4s/v5/public/uicard/" +
            props.competitionSummaryPublic.compId,
          targetName: "_result-entry",
        });

        links.push({
          id: links.length,
          name: "Scoreboard",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.SCOREBOARD_OUTPUT_LIST +
            "/" +
            props.competitionSummaryPublic.compId,
        });
      }

      if (compPermissions.hasAutoEntryAccess.value) {
        // links.push({
        //   id: links.length,
        //   name: "---------",
        //   link: "SPACER_LINE",
        //   disabled: true
        // });

        links.push({
          id: links.length,
          name: "Auto Entry",
          link:
            "/" +
            LAUNCH_ROUTES_PATHS.AUTO_ENTRIES +
            "/" +
            props.competitionSummaryPublic.compId,
        });
      }

      return sortArray("name", links);
    });

    function goToLink() {
      const link = selectedLink.value;
      if (link.targetName && link.targetName.length > 0) {
        window.open(link.link, link.targetName);
        return;
      }
      let location: RawLocation = {
        path: link.link,
      };
      router.push(location);
    }

    return { selectedLink, getLinks, goToLink };
  },
});
</script>
