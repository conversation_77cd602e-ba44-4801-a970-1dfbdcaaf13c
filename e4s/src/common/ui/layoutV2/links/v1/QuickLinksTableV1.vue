<template>
  <div class="e4s-flex-column e4s-gap--standard">
    <div
      class="e4s-flex-row"
      v-for="link in quickLinksControllerV1.getLinks.value"
      :key="link.id"
    >
      <PrimaryLink
        style="width: 140px"
        :link-text="link.name"
        @onClick="gotoLink(link)"
      />

      <span v-text="link.description ? link.description : ''"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import { useQuickLinksControllerV1 } from "./useQuickLinksControllerV1";
import PrimaryLink from "../../href/PrimaryLink.vue";
import { IPublicListCompCardLink } from "../../../../../public/entry-public/public-list/v2/public-list-comp-card-models";

export default defineComponent({
  name: "QuickLinksTableV1",
  components: { PrimaryLink },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    const quickLinksControllerV1 = useQuickLinksControllerV1(
      props.competitionSummaryPublic
    );

    function gotoLink(link: IPublicListCompCardLink) {
      if (link.emitEventName && link.emitEventName === "clone-competition") {
        context.emit("clone-competition", link);
        return;
      }

      quickLinksControllerV1.goToLink(link);
    }

    return { quickLinksControllerV1, gotoLink };
  },
});
</script>
