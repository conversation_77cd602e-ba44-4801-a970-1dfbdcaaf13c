import { ICompetitionSummaryPublic } from "../../../../../competition/competition-models";
import { useCompPermissions } from "../../../../../config/useCompPermissions";
import { useRouter } from "../../../../../router/migrateRouterVue3";
import { computed, ComputedRef, ref } from "@vue/composition-api";
import { IPublicListCompCardLink } from "../../../../../public/entry-public/public-list/v2/public-list-comp-card-models";
import { LAUNCH_ROUTES_PATHS } from "../../../../../launch/launch-routes";
import { CONFIG } from "../../../../config";
import { sortArray } from "../../../../common-service-utils";
import { RawLocation } from "vue-router";

export function useQuickLinksControllerV1(
  competitionSummaryPublic: ICompetitionSummaryPublic
) {
  const compPermissions = useCompPermissions(competitionSummaryPublic);
  const router = useRouter();

  const selectedLink = ref<IPublicListCompCardLink>({
    id: 0,
    name: "",
    link: "",
    targetName: "",
    description: "",
  });

  const getLinks: ComputedRef<IPublicListCompCardLink[]> = computed<
    IPublicListCompCardLink[]
  >(() => {
    const links: IPublicListCompCardLink[] = [];

    if (compPermissions.hasBuilderPermissionForComp.value) {
      // links.push({
      //   id: links.length,
      //   name: "---------",
      //   link: "SPACER_LINE",
      //   disabled: true
      // });

      links.push({
        id: links.length,
        name: "Builder",
        description: "Edit competition details.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.BUILDER +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Bibs",
        description: "Bib management.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.BIBS +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Clone",
        link: "",
        description: "Duplicate a previous competition without entries.",
        emitEventName: "clone-competition",
      });

      links.push({
        id: links.length,
        name: "Email\\Message",
        description: "Send emails and messages to athletes.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.EMAIL_MESSAGES +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Organiser Report",
        description: "View organiser report.",
        link:
          CONFIG.E4S_HOST + "/" + competitionSummaryPublic.compId + "/report",
        targetName: "_organiser-report",
      });
    }

    if (compPermissions.hasTicketPermissionForComp.value) {
      links.push({
        id: links.length,
        name: "Tickets (Scan)",
        description: "Scan spectator tickets to allow site entry.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.TICKET_FORM_GATEKEEPER +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Tickets (Admin)",
        description: "View and manage spectator tickets.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.TICKET_ADMIN +
          "/" +
          competitionSummaryPublic.compId,
      });
    }

    if (compPermissions.hasCheckinPermissionForComp.value) {
      // links.push({
      //   id: links.length,
      //   name: "---------",
      //   link: "SPACER_LINE",
      //   disabled: true
      // });

      links.push({
        id: links.length,
        name: "Check-in",
        description: "Check-in/register athletes on the day.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.CHECKIN_ORGANISER +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Photo Finish",
        description: "Manage photo finish files.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.RESULTS_IMPORT_FILE +
          "/" +
          competitionSummaryPublic.compId,
      });

      // links.push({
      //   id: links.length,
      //   name: "Result Entry",
      //   description: "Enter results for athletes.",
      //   link:
      //     CONFIG.E4S_HOST +
      //     "/wp-json/e4s/v5/public/uicard/" +
      //     competitionSummaryPublic.compId,
      //   targetName: "_result-entry",
      // });

      links.push({
        id: links.length,
        name: "Result Entry",
        description: "Enter results for athletes.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC +
          "/" +
          competitionSummaryPublic.compId,
      });

      links.push({
        id: links.length,
        name: "Scoreboard",
        description: "Set up and view scoreboards.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.SCOREBOARD_OUTPUT_LIST +
          "/" +
          competitionSummaryPublic.compId,
      });
    }

    if (compPermissions.hasAutoEntryAccess.value) {
      // links.push({
      //   id: links.length,
      //   name: "---------",
      //   link: "SPACER_LINE",
      //   disabled: true
      // });

      links.push({
        id: links.length,
        name: "Auto Entry",
        description: "View and manage auto entries.",
        link:
          "/" +
          LAUNCH_ROUTES_PATHS.AUTO_ENTRIES +
          "/" +
          competitionSummaryPublic.compId,
      });
    }

    links.push({
      id: links.length,
      name: "Schedule",
      description: "View competition schedule.",
      link:
        CONFIG.E4S_HOST +
        "/entry/v5/competition/schedule.php?compid=" +
        competitionSummaryPublic.compId,
      targetName: "_schedule",
    });

    if (
      competitionSummaryPublic.link &&
      competitionSummaryPublic.link.length > 0
    ) {
      links.push({
        id: links.length,
        name: "Flyer",
        description: "View competition flyer.",
        link: competitionSummaryPublic.link,
        targetName: "_flyer",
      });
    }

    links.push({
      id: links.length,
      name: "Seeding",
      description: "View seeding.",
      link:
        CONFIG.E4S_HOST +
        "/wp-json/e4s/v5/public/reports/cards/" +
        competitionSummaryPublic.compId,
      targetName: "_seeding",
    });

    return sortArray("name", links);
  });

  function goToLink(link: IPublicListCompCardLink) {
    selectedLink.value = link;
    if (link.targetName && link.targetName.length > 0) {
      window.open(link.link, link.targetName);
      return;
    }
    const location: RawLocation = {
      path: link.link,
    };
    router.push(location);
  }

  return { selectedLink, getLinks, goToLink };
}
