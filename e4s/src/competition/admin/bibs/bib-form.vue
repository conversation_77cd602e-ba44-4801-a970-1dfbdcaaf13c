<template>
  <div>
    <LoadingSpinnerV2 v-if="isLoading" />
    <FormHeader title="Bib Generation">
      <CompetitionGoTo
        slot="e4s-form-header--right"
        :comp-id="competitionSummaryPublic.compId"
        class="right"
      ></CompetitionGoTo>
    </FormHeader>

    <div class="row">
      <div class="col s12 m12 l12">
        As the Organiser, you have the ability to generate Bib Numbers whenever
        you require. The rules for the bib number generation ( Starting numbers,
        sorting e.t.c ) can be configured within the builder .
        <BuilderGoTo :comp-id="competitionSummaryPublic.compId" />
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row" v-if="isMultiDayComp">
      <div class="col s12 m12 l12">
        <h5 class="e4s-header--500">This is a multi-day competition.</h5>
        The bib numbers will be generated for selected date. If no date is
        selected, the bib numbers will be generated for all dates.

        <div class="e4s-flex-row e4s-gap--x-large">
          <FieldRadioV2
            option-value=""
            v-model="createBisForDate"
            label="ALL"
          />
          <FieldRadioV2
            v-for="date in competitionSummaryPublic.dates"
            :key="date"
            :option-value="date"
            v-model="createBisForDate"
            :label="date"
          />
        </div>

        {{ createBisForDate }}
        <div class="e4s-section-padding-separator"></div>
      </div>
    </div>

    <div class="row">
      <div class="col s12 m12 l12">You have the following options :</div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        a) To clear all bib numbers for this competition.

        <ButtonsCancelOkConfirm
          ok-text="Clear"
          :show-cancel-only-when-confirming="true"
          :is-loading="isLoading"
          v-on:ok="proceed('CLEAR')"
        ></ButtonsCancelOkConfirm>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        b) To generate bib numbers for all athletes for this competition,
        overwriting previously allocated numbers.

        <ButtonsCancelOkConfirm
          ok-text="Create"
          :show-cancel-only-when-confirming="true"
          :is-loading="isLoading"
          v-on:ok="proceed('NEW')"
        ></ButtonsCancelOkConfirm>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        c) If you have already generated bib numbers and there are some new
        athletes but you dont want to change the existing bib numbers.

        <ButtonsCancelOkConfirm
          ok-text="Additional"
          :show-cancel-only-when-confirming="true"
          :is-loading="isLoading"
          v-on:ok="proceed('ADD')"
        ></ButtonsCancelOkConfirm>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  ref,
  SetupContext,
} from "@vue/composition-api";
import FormHeader from "../../../common/ui/form/header/form-header.vue";
import CompetitionGoTo from "../../ui/competition-go-to.vue";
import { ICompetitionSummaryPublic } from "../../competition-models";
import ButtonsCancelOkConfirm from "../../../common/ui/buttons/buttons-cancel-ok-confirm.vue";
import { BibData } from "./bib-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import BuilderGoTo from "../../../builder/form/BuilderGoTo.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import FieldRadioV2 from "../../../common/ui/layoutV2/fields/field-radio-v2.vue";

export default defineComponent({
  name: "BibForm",
  components: {
    FieldRadioV2,
    LoadingSpinnerV2,
    CompetitionGoTo,
    BuilderGoTo,
    ButtonsCancelOkConfirm,
    FormHeader,
  },
  props: {
    competitionSummaryPublic: {
      required: true,
      type: Object as PropType<ICompetitionSummaryPublic>,
    },
  },
  setup(
    props: { competitionSummaryPublic: ICompetitionSummaryPublic },
    context: SetupContext
  ) {
    const isLoading = ref(false);
    const createBisForDate = ref("");

    const isMultiDayComp = computed(() => {
      return props.competitionSummaryPublic.dates.length > 1;
    });

    function proceed(action: "CLEAR" | "NEW" | "ADD") {
      isLoading.value = true;
      const prom = new BibData().processBibNumbers(
        props.competitionSummaryPublic.compId,
        action,
        createBisForDate.value
      );
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          console.log("resp", resp);
        })
        .finally(() => {
          isLoading.value = false;
        });
    }

    function setBibDate(date: string) {
      createBisForDate.value = date;
    }

    return { isLoading, createBisForDate, isMultiDayComp, proceed, setBibDate };
  },
});
</script>
