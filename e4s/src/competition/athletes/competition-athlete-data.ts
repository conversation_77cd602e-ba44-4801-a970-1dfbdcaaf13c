import {IServerResponse, IsoDate} from "../../common/common-models";
import https from "../../common/https";
import {ICompetitionAthleteSummary} from "./competition-athlete-models";

export class CompetitionAthleteData {

  public search(
    compId: number,
    key: string,
    pageSize: number = 20,
    sortKey: string = "surname"
  ): Promise<IServerResponse<Record<number, ICompetitionAthleteSummary>>> {
    return https.get(
      "/v5/competition/" + compId + "/athletesummary?" + "pagesize=" + pageSize + "&startswith=" + key
    ) as any as Promise<IServerResponse<Record<number, ICompetitionAthleteSummary>>>;
  }

  public validateAthleteRelationship(athleteId: number, dateOfBirth: IsoDate): Promise<IServerResponse<any>> {
    return https.get(
      "/v5/athlete/link/" + athleteId + "/" + dateOfBirth
    ) as any as Promise<IServerResponse<any>>;
  }
}
