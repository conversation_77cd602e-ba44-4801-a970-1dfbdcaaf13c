<template>
    <div>

        <div class="row">
            <div class="input-field col s12 m12 l12">
                Only first few characters required when searching.
            </div>
        </div>

        <div class="e4s-section-padding-separator"></div>

        <div class="row">
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'first-name'"
                    :name="PREFIX + 'first-name'"
                    class="e4s-input"
                    type="text"
                    v-model="state.firstName"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'first-name'">
                    First Name
                    <FieldValidationLabel :validation-controller="validationController" :prop-path="'state.firstName'"/>
                </label>
            </div>
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'sur-name'"
                    :name="PREFIX + 'sur-name'"
                    class="e4s-input"
                    type="text"
                    v-model="state.surName"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'sur-name'">
                    Last Name
                    <FieldValidationLabel :validation-controller="validationController" :prop-path="'state.surName'"/>
                </label>
            </div>
        </div>

        <div class="row">
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'urn'"
                    :name="PREFIX + 'urn'"
                    class="e4s-input"
                    type="text"
                    v-model="state.urn"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'urn'">
                    URN
                </label>
            </div>
            <div class="input-field col s6 m6 l6">
                <input
                    :id="PREFIX + 'club'"
                    :name="PREFIX + 'club'"
                    class="e4s-input"
                    type="text"
                    v-model="state.club"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'club'">
                    Club
                </label>
            </div>
            <div class="input-field col s6 m6 l6" v-if="showBibInputField">
                <input
                    :id="PREFIX + 'bib-no'"
                    :name="PREFIX + 'bib-no'"
                    type="text"
                    v-model="state.bibNo"
                    v-on:keyup.enter="search"
                    placeholder=""/>
                <label class="active" :for="PREFIX + 'bib-no'">
                    Bib Number
                </label>
            </div>
        </div>

        <div class="row">

            <div class="input-field col s12 m12 l6">
                <label class="active" :for="PREFIX + 'date'">
                    Date
                </label>
                <select v-model="state.date"
                        v-on:change="search"
                        class="browser-default">
                    <option v-for="d in checkInStoreState.checkinSummary.dates" :value="d">
                        <span v-text="d"></span>
                    </option>
                </select>
            </div>
        </div>


        <slot name="buttons-section">
            <div class="row">
                <div class="col s12 m12 l12">
                    <LoadingSpinner v-if="getIsSomethingLoading" class="e4s-force-inline-block"></LoadingSpinner>
                    <div class="right">

                        <slot name="qr-link">
                            <a :href="'#/' + LAUNCH_ROUTES_PATHS.CHECKIN_QR_ROUTE + '/' + checkInStoreState.checkinSummary.id">
                                <span>Open QR Print</span>
                            </a>
                        </slot>

                        <div class="e4s-force-inline-block">

                            <slot name="button-checkin-clear-down">
                                <button class="btn waves-effect waves red"
                                        :disabled="getIsSomethingLoading"
                                        v-on:click="showClearDownDialog = true"
                                >Clear Down</button>
                            </slot>
                            <slot name="button-checkin-other-buttons"></slot>
                            <button class="btn waves-effect waves red" :disabled="getIsSomethingLoading" v-on:click="reset">Reset</button>
                            <button class="btn waves-effect waves green" :disabled="getIsSomethingLoading" v-on:click="search">Search</button>
                        </div>
                    </div>
                </div>
            </div>
        </slot>

    </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Component from "vue-class-component";
import Vue from "vue";
import FieldValidationLabel from "../../../../validation/validation-field-lable.vue";
import FieldHelp from "../../../../common/ui/field/field-help/field-help.vue";
import {ICheckInStoreState} from "../../checkin-models";
import {ValidationController} from "../../../../validation/validation-controller";
import {BIB_COLLECTED_STATUS, CheckinData, IGetCheckinSearchParams} from "../../checkin-data";
import {format} from "date-fns";
import CheckinUser from "../checkin-user.vue";
import CollapseSection from "../../../../common/ui/collapse/collapse-section.vue";
import {mapGetters, mapState} from "vuex";
import {CONFIG_STORE_CONST} from "../../../../config/config-store";
import {CHECKIN_STORE_CONST} from "../../checkin-store";
import {Watch, Prop} from "vue-property-decorator";
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import {ConfigService} from "../../../../config/config-service"
import {IEntity} from "../../../../config/config-app-models"
import {CheckinService} from "../../checkin-service"
import CheckinCleardown from "../checkin-cleardown.vue"
import {CommonService} from "../../../../common/common-service"

@Component({
    name: "checkin-athlete-search",
    components: {
        CheckinCleardown,
        CollapseSection,
        CheckinUser,
        FieldValidationLabel,
        FieldHelp
    },
    computed: {
        ...mapGetters(
            {
                isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
                getHeaderTitle: CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_GETTERS_TITLE_HEADER
            }
        ),
        ...mapState(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME, {
            checkInStoreState: (state: ICheckInStoreState) => state,
            checkInDates: (state: ICheckInStoreState) => state.checkinSummary.dates
        })
    }
})
export default class CheckinAthleteSearch extends Vue {
    public readonly checkInStoreState: ICheckInStoreState
    public readonly checkInDates: string[];

    @Prop({default: 0})
    public readonly compId: number;

    @Prop({default: false})
    public readonly showBibInputField: boolean;

    @Prop({default: false})
    public readonly isLoading: boolean;

    public configService: ConfigService = new ConfigService();
    public checkInService: CheckinService = new CheckinService();
    public checkinData: CheckinData = new CheckinData();
    public commonService = new CommonService();
    // public athleteCheckinInternal: ICheckinAthlete[] = [];

    public entity: IEntity = this.configService.factoryEntity();

    public LAUNCH_ROUTES_PATHS = LAUNCH_ROUTES_PATHS;

    public state = {
        compId: 0,
        firstName: "",
        surName: "",
        club: "",
        date: "",
        urn: "",
        bibNo: "",
        dob: "",
        showSaveAllConfirm: false
    };
    public PREFIX = Math.random().toString(36).substring(2);
    public validationController: ValidationController = new ValidationController();

    public showClearDownDialog: boolean = false;

    public created() {
        this.state.compId = this.compId;
        console.log("RegistrationOrganiser.created() this.state.compId: " + this.state.compId);

        if (this.checkInDates.length > 0 ) {
            this.state.date = this.checkInDates[0];
        } else {
            this.state.date = format(new Date(), "YYYY-MM-DD");
        }

        //  reset "grid"
        // this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
        //     CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);
        //
        // if (this.state.compId > 0) {
        //     this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
        //         CHECKIN_STORE_CONST.CHECKIN_ACTIONS_GET_SUMMARY, this.compId)
        //         .then( () => {
        //             this.state.date = this.checkInStoreState.checkinSummary.dates[0];
        //         })
        // }

        //  TODO
    }

    @Watch("checkInStoreState")
    public onCheckInStoreStateChanged(newValue: ICheckInStoreState, oldValue: ICheckInStoreState) {
        if (newValue.checkinSummary.dates.length > 0) {
            this.state.date = this.checkInStoreState.checkinSummary.dates[0];
        }
    }

    @Watch("checkInDates")
    public onCheckInDatesChanged(newValue: string[], oldValue: string[]) {
        if (newValue.length > 0) {
            this.state.date = newValue[0];
        }
    }

    // @Watch("athleteCheckin")
    // public onAthleteCheckinChanged(newValue: ICheckinAthlete[]) {
    //     this.athleteCheckinInternal = R.clone(newValue);
    // }

    public getState() {
        return {
            compId: 0,
            firstName: "",
            surName: "",
            club: "",
            date: "",
            urn: "",
            bibNo: "",
            dob: "",
            showSaveAllConfirm: false
        };
    }

    public reset() {
        const stateNew = {
            ...this.getState(),
            compId: this.state.compId
        };
        stateNew.date = this.checkInStoreState.checkinSummary.dates[0];
        this.state = stateNew;

        this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);
    }

    public search() {
        const searchPayload: IGetCheckinSearchParams = {
            compId: this.state.compId,
            isoDate: this.state.date,
            firstName: this.state.firstName,
            lastName: this.state.surName,
            club: this.state.club,
            entity: this.entity,
            urn: this.state.urn,
            bibNo: this.state.bibNo,
            dob: this.state.dob,
            eventName: "",
            pageNumber: 1,
            pageSize: 100,
            collected: BIB_COLLECTED_STATUS.ALL,
            nonce: "",
            isOrganiser: true,
            whois: false
        };
        this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_ACTIONS_SEARCH, searchPayload);
    }

    public get getIsSomethingLoading() {
        return this.isLoading || this.checkInStoreState.isLoading || this.checkInStoreState.isLoadingSummary;
    }

    public get showSaveAll(): boolean {
        return this.checkInStoreState.athleteCheckin.length > 1;
    }

    public get getCompSummaryLoaded() {
        return this.checkInStoreState.checkinSummary.id > 0;
    }

}
</script>
