<template>
  <div>
    <div class="e4s-simple-form--header">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="left">
            Bib: <span v-text="getBibDetails"></span>
            &nbsp
            <span
              class="e4s-simple-form--header-text"
              :id="PREFIX + 'get-name'"
              v-text="getName"
            ></span>
          </div>
          <div class="right">
            <span v-text="getCheckinMessage"></span>
            <div
              class="e4s-force-inline-block check-in-indicator"
              :class="getCheckInIndicatorStatusCss"
            ></div>
          </div>
        </div>
      </div>

      <div class="row e4s-simple-form--header-sub-text-row">
        <div class="col s12 m12 l12">
          <div class="left">
            <span
              class="e4s-simple-form--header-sub-text"
              v-text="getHeaderInfoLeft"
            ></span>
          </div>
          <div class="right">
            <span
              class="e4s-simple-form--header-sub-text"
              v-text="getHeaderInfoRight"
            ></span>
          </div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" for="checkin-entries">
          Confirm events (tick / un-tick): (isDirty:
          {{ formController.isDirty }})
        </label>

        <span
          v-if="formController.isDirty"
          class="e4s-header--400 e4s-info-text--error"
          >Changes made, save required.</span
        >
        <a
          href="#"
          class="right"
          v-on:click.prevent="showAddNotes = !showAddNotes"
          ><span v-text="showAddNotes ? 'Hide' : 'Show'"></span> Notes Editor</a
        >
      </div>
    </div>

    <div class="row" v-if="isOrganiser">
      <div class="col s12 m12 l12">
        <label class="active" for="checkin-entries--change-bib">
          Change Bib Number:
        </label>
        <div class="e4s-force-inline-block">
          <input
            class="e4s-input e4s-input-small-number"
            v-model.number="changeBibNumberTo"
            name="checkin-entries--change-bib"
            id="checkin-entries--change-bib"
          />
        </div>

        <button
          class="btn waves-effect waves green"
          :disabled="changeBibNumberTo <= 0"
          v-on:click="changeBibNo"
        >
          Change
        </button>
      </div>
    </div>

    <div class="row" v-if="showAddNotes">
      <div class="col s12 m12 l12">
        <InputRestrictLength
          :use-text-area="true"
          :max-length="500"
          text-area-class="checkin-user--notes"
          v-model="athleteCheckinInternal.notes"
          v-on:onChanged="formController.processChangesDebounce()"
        />
      </div>
    </div>

    <div
      class="row"
      v-if="
        !showAddNotes &&
        athleteCheckinInternal.notes &&
        athleteCheckinInternal.notes.length > 0
      "
    >
      <div class="col s12 m12 l12">
        <div class="checkin-user--notes-read-mode">
          Athlete Notes:
          <div v-text="athleteCheckinInternal.notes"></div>
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div id="checkin-entries">
      <div
        v-for="(entry, index) in getEntries"
        class="e4s-card-std"
        :class="index % 2 === 0 ? '' : 'e4s-card-std__row-odd'"
        :key="entry.id"
      >
        <CheckinUserEntry
          style="padding: 0 var(--e4s-gap--large)"
          :athlete-entry="entry"
          :is-event-disabled="areEventCheckBoxesDisabled"
          v-on:onChange="entrySelected"
          v-on:showOptions="showOptions"
        >
        </CheckinUserEntry>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="input-field col s12 m6 l6">
        <label class="active"> Check in time for this athlete: </label>
        <div v-text="getCheckinTimeRangeMessage"></div>
        <div v-text="getCheckinSuggestedMessage"></div>
      </div>

      <div class="input-field col s12 m6 l6">
        <label class="active"> Confirmed Time: </label>
        <span v-text="getConfirmedTime"></span>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m6 l6" v-if="showBibCollected">
        <label>
          <input
            :id="PREFIX + 'bib-collected'"
            class="e4s-checkbox"
            type="checkbox"
            v-on:change="submit"
            :disabled="!isOrganiser"
            v-model="athleteCheckinInternal.collected"
          />
          <span> Bib Collected <span v-text="getCollectedTime"></span> </span>
        </label>
      </div>

      <!--User has access to the bib check box.-->
      <div class="col s12 m6 l6" v-if="showBibCollected">
        <span v-if="!isOrganiser && getHasUserConfirmed"
          >Proceed to Registration</span
        >
        <button
          class="btn waves-effect waves green right"
          :disabled="getIsSaveDisabled"
          v-on:click="submit"
        >
          Save
        </button>
      </div>
    </div>
    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Component from "vue-class-component";
import Vue from "vue";
import FieldValidationLabel from "../../../validation/validation-field-lable.vue";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
import { Prop, Watch } from "vue-property-decorator";
import {
  ICheckinAthlete,
  IAthleteEntry,
  CHECKIN_STATUS,
  ICheckinCompSummary,
  IAthleteCheckinDetails,
} from "../checkin-models";
import { CheckinService } from "../checkin-service";
import { ValidationController } from "../../../validation/validation-controller";
import { FormController } from "../../../common/ui/form/form-controller/form-controller";
import { parse, format, isAfter, isBefore } from "date-fns";
import CheckinUserEntry from "./checkin-user-entry.vue";
import { CommonService } from "../../../common/common-service";
import InputRestrictLength from "../../../common/ui/field/input-restrict-length/input-restrict-length.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { simpleClone } from "../../../common/common-service-utils";

const checkinService: CheckinService = new CheckinService();

@Component({
  name: "checkin-user",
  components: {
    LoadingSpinnerV2,
    InputRestrictLength,
    CheckinUserEntry,
    FieldValidationLabel,
    FieldHelp,
  },
})
export default class CheckinUser extends Vue {
  @Prop({
    default: () => {
      return checkinService.factoryCheckinAthlete();
    },
  })
  public readonly athleteCheckin: ICheckinAthlete;
  @Prop({
    default: () => {
      return checkinService.factoryCheckinCompSummary();
    },
  })
  public readonly checkinCompSummary: ICheckinCompSummary;
  @Prop({
    default: false,
  })
  public readonly isLoading: boolean;
  @Prop({
    default: true,
  })
  public readonly showBibCollected: boolean;
  @Prop({
    default: false,
  })
  public readonly disableBibCollected: boolean;
  @Prop({
    default: false,
  })
  public readonly isOrganiser: boolean;
  @Prop({
    default: "",
  })
  public readonly calcAgeDate: string;
  @Prop({
    required: true,
  })
  public readonly commonService: CommonService;

  public athleteCheckinInternal: ICheckinAthlete =
    checkinService.factoryCheckinAthlete();
  public athleteCheckinOriginal: ICheckinAthlete =
    checkinService.factoryCheckinAthlete();
  public PREFIX = Math.random().toString(36).substring(2);
  public validationController: ValidationController =
    new ValidationController();
  public formController: FormController = new FormController(
    this.athleteCheckin,
    this.athleteCheckinInternal
  );
  public checkinService = checkinService;

  public showAddNotes = false;
  public isFormDirty = false;

  public changeBibNumberTo = 0;

  public created() {
    this.athleteCheckinOriginal = R.clone(this.athleteCheckin);
    this.athleteCheckinInternal = R.clone(this.athleteCheckin);
    // this.formController.setSources(this.athleteCheckin, this.athleteCheckinInternal);
    console.log("CheckinUser.created() bibNo: " + this.athleteCheckin.bibNo);
    this.setFromController();
  }

  @Watch("athleteCheckin")
  public onAthleteCheckinChange(
    newValue: ICheckinAthlete,
    oldValue: ICheckinAthlete
  ) {
    // they don't have newValue.id
    console.log(
      "CheckinUser.Watch(athleteCheckin) bibNo: " + this.athleteCheckin.bibNo
    );
    this.athleteCheckinInternal = R.clone(newValue);
    // this.formController.setSources(this.athleteCheckin, this.athleteCheckinInternal);
    this.setFromController();
  }

  @Watch("formController.isDirty")
  public onFormControllerIsDirtyChange(
    newValue: boolean,
    oldValue: boolean
  ): void {
    console.log("CheckinUser.Watch(formController.isDirty)");
    this.isFormDirty = newValue;
    this.$emit("isDirtyChange", {
      isDirty: newValue,
      athleteCheckin: simpleClone(this.athleteCheckinInternal),
    });
  }

  public setFromController() {
    if (
      this.athleteCheckin.bibNo > 0 &&
      this.athleteCheckinInternal.bibNo > 0
    ) {
      this.formController.setSources(
        this.athleteCheckinOriginal,
        this.athleteCheckinInternal
      );
    }
  }

  public get getName(): string {
    return (
      this.athleteCheckinInternal.firstName +
      " " +
      this.athleteCheckinInternal.surName
    );
  }

  public get getDOB(): string {
    if (this.athleteCheckinInternal.dob.length === 0) {
      return "";
    }
    const calcAgeDate =
      this.calcAgeDate.length > 0
        ? this.calcAgeDate
        : format(new Date(), "YYYY-MM-DD");
    const age = this.commonService.ageBetweenDates(
      this.athleteCheckinInternal.dob,
      calcAgeDate
    );

    return (
      format(parse(this.athleteCheckinInternal.dob), "Do MMM YYYY") +
      " (" +
      age.message +
      ")"
    );
  }

  public getEntryName(entry: IAthleteEntry) {
    return entry.ageGroup + " - " + entry.name;
  }

  public isEventCheckInDisabled(entry: IAthleteEntry) {
    const dateTimeNow = new Date();

    if (entry.checkInFrom > -1) {
      const checkInFromFns = parse(entry.checkInFrom);
      if (isBefore(dateTimeNow, checkInFromFns)) {
        return true;
      }
    }

    if (entry.checkInTo > -1) {
      const checkInToFns = parse(entry.checkInTo);
      if (isAfter(dateTimeNow, checkInToFns)) {
        return true;
      }
    }
    return false;
  }

  public entrySelected(entry: IAthleteEntry) {
    this.athleteCheckinInternal.entries =
      this.athleteCheckinInternal.entries.map((ent) => {
        if (entry.id === ent.id) {
          return entry;
        }
        return ent;
      });

    this.formController.processChanges();
    this.onChange();
  }

  public generalOnChange(): void {
    this.formController.processChanges();
    this.onChange();
  }

  public get getHeaderInfoLeft() {
    return (
      (this.athleteCheckinInternal.gender === "F" ? "Female" : "Male") +
      " / " +
      this.getDOB
    );
  }

  public get getHeaderInfoRight() {
    const urn = this.athleteCheckinInternal?.urn
      ? this.athleteCheckinInternal.urn + ""
      : "";
    const club = this.athleteCheckinInternal.club
      ? this.athleteCheckinInternal.club
      : "Unattached";
    return club + (urn.length > 0 ? " / " + urn : "");
  }

  public get getHasUserConfirmed() {
    return (
      this.athleteCheckinInternal.confirmedTime &&
      this.athleteCheckinInternal.confirmedTime.length > 0
    );
  }

  public get getConfirmedTime() {
    return this.athleteCheckinInternal.confirmedTime &&
      this.athleteCheckinInternal.confirmedTime.length > 0
      ? format(
          parse(this.athleteCheckinInternal.confirmedTime),
          "HH:mm Do MMM YYYY"
        )
      : "";
  }

  public get getCollectedTime() {
    return this.athleteCheckinInternal.collectedTime &&
      this.athleteCheckinInternal.collectedTime.length > 0
      ? format(
          parse(this.athleteCheckinInternal.collectedTime),
          "HH:mm Do MMM YYYY"
        )
      : "";
  }

  public getEventStartTime(entry: IAthleteEntry) {
    return format(parse(entry.dateTime), "hh:mma");
  }

  public get getCheckInDetails(): IAthleteCheckinDetails {
    //  TODO
    // const pointInTime = new Date(2020, 4, 28, 9, 1, 0);
    const pointInTime = new Date();
    const checkInDateTimeOpens =
      this.checkinCompSummary.checkIn.checkInDateTimeOpens;
    let compDateOpens = null;
    if (checkInDateTimeOpens.length > 0) {
      compDateOpens = parse(checkInDateTimeOpens);
    }

    return this.checkinService.getAthleteCheckinDetails(
      this.checkinCompSummary.checkIn,
      this.athleteCheckinInternal,
      pointInTime,
      compDateOpens
    );
  }

  public get getCheckinMessage() {
    return this.getCheckInDetails.message;
  }

  public get getCheckinTimeRangeMessage() {
    return this.getCheckInDetails.checkInRangeMessage;
  }

  public get getCheckinSuggestedMessage() {
    return "Registration: " + this.getCheckInDetails.suggestedAthleteMessage;
  }

  public onChange() {
    this.$emit("onChange", R.clone(this.athleteCheckinInternal));
  }

  public submit() {
    this.$emit("onSubmit", R.clone(this.athleteCheckinInternal));
  }

  public get getCheckInStatusCss() {
    const cssMap: Record<CHECKIN_STATUS, string> = {
      ["NOT_YET_OPEN"]: "check-in-not-yet-open",
      ["OPEN"]: "check-in-open",
      ["CLOSED"]: "check-in-closed",
    };
    if (cssMap[this.getCheckInDetails.status]) {
      return cssMap[this.getCheckInDetails.status];
    }
    return "";
  }

  public get getCheckInIndicatorStatusCss() {
    const cssMap: Record<CHECKIN_STATUS, string> = {
      ["NOT_YET_OPEN"]: "check-in-bg-not-yet-open",
      ["OPEN"]: "check-in-bg-open",
      ["CLOSED"]: "check-in-bg-closed",
    };
    if (cssMap[this.getCheckInDetails.status]) {
      return cssMap[this.getCheckInDetails.status];
    }
    return "";
  }

  public get areEventCheckBoxesDisabled() {
    if (this.isOrganiser) {
      return false;
    }
    return this.getHasUserConfirmed || this.getCheckInDetails.status !== "OPEN";
  }

  // public get getIsProbablyAthleteAndConfirmed() {
  //     return this.disableBibCollected && this.getHasUserConfirmed;
  // }

  public get getIsSaveDisabled() {
    return (
      this.isLoading ||
      !this.formController.isDirty ||
      (!this.isOrganiser && this.getHasUserConfirmed)
    );
  }

  public get getShouldBibCheckBoxBeDisabled() {
    const haveAnyEventsBeenSelected =
      new CheckinService().haveAnyEventsBeenSelected(
        this.athleteCheckinInternal
      );
    return this.disableBibCollected || !haveAnyEventsBeenSelected;
  }
  public get getEntries() {
    return this.athleteCheckinInternal.entries.sort(
      (a: IAthleteEntry, b: IAthleteEntry) => {
        return a.dateTime.localeCompare(b.dateTime);
      }
    );
  }

  public get getBibDetails(): string {
    let bibDetails: string = this.athleteCheckinInternal.bibNo.toString();
    if (
      this.athleteCheckinInternal.teamBibNo &&
      this.athleteCheckinInternal.teamBibNo.length > 0
    ) {
      bibDetails += " / " + this.athleteCheckinInternal.teamBibNo;
    }
    return bibDetails;
  }

  public changeBibNo() {
    this.$emit("changeBibNumber", {
      compId: this.checkinCompSummary.id,
      athleteId: this.athleteCheckin.athleteId,
      bibNo: this.changeBibNumberTo,
      athleteCheckin: this.athleteCheckin,
    });
  }

  public showOptions(athleteEntry: IAthleteEntry) {
    this.$emit("showOptions", {
      athleteId: this.athleteCheckin.athleteId,
      entryId: athleteEntry.id,
    });
  }
}
</script>

<style scoped>
.check-in-not-yet-open {
  color: orange;
}
.check-in-open {
  color: #0f9d58;
}
.check-in-closed {
  color: red;
}
.check-in-indicator {
  width: 0.5em;
  height: 1em;
  /*background-color: #00acc1;*/
}

.check-in-bg-not-yet-open {
  background-color: orange;
}
.check-in-bg-open {
  background-color: #0f9d58;
}
.check-in-bg-closed {
  background-color: red;
}

.checkin-user--entries {
  padding: 0.5em 0 0.5em 0;
}

.checkin-user--notes {
  height: 10rem !important;
}

.checkin-user--notes-read-mode {
  color: blue;
  font-weight: 600;
}
</style>
