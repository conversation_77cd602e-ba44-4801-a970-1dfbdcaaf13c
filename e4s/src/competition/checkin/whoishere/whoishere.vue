<template>
    <div>

        <CollapseSection icon-name="accessibility"
                         :allow-expand-collapse = "false"
                         :header-message="'Who\'s here for: ' + getHeaderTitle"
                         :is-expanded="true">

            <div slot="section-content">
                <div class="row">
                    <div class="input-field col s12 m12 l6">
                        <input
                                :id="PREFIX + 'first-name'"
                                :name="PREFIX + 'first-name'"
                                type="text"
                                v-model="state.firstName"
                                v-on:keyup.enter="search"
                                placeholder=""/>
                        <label class="active" :for="PREFIX + 'first-name'">
                            First Name
                        </label>
                    </div>
                    <div class="input-field col s12 m12 l6">
                        <input
                                :id="PREFIX + 'sur-name'"
                                :name="PREFIX + 'sur-name'"
                                type="text"
                                v-model="state.surName"
                                v-on:keyup.enter="search"
                                placeholder=""/>
                        <label class="active" :for="PREFIX + 'sur-name'">
                            Last Name
                        </label>
                    </div>
                </div>

                <div class="row">
                    <div class="input-field col s12 m12 l6">
                        <label class="active" :for="PREFIX + 'status'">
                            Event
                        </label>
                        <SimpleObjectDropDown :current-value="getEventGroupDefault"
                                              :simple-objects="getEventGroups"
                                              v-on:onSelected="state.event = $event.name">
                        </SimpleObjectDropDown>
                    </div>
                    <div class="input-field col s12 m12 l6">
                        <input
                                :id="PREFIX + 'club'"
                                :name="PREFIX + 'club'"
                                type="text"
                                v-model="state.club"
                                v-on:keyup.enter="search"
                                placeholder=""/>
                        <label class="active" :for="PREFIX + 'club'">
                            Club
                        </label>
                    </div>
                </div>

                <div class="row">
                    <div class="input-field col s12 m12 l12">
                        <label class="active" :for="PREFIX + 'status'">
                            Arrival Status
                        </label>
<!--                        <select v-model="state.status" :id="PREFIX + 'status'" class="browser-default">-->
<!--                            <option :value="bibCollectedStatuses.ALL">All</option>-->
<!--                            <option :value="bibCollectedStatuses.COLLECTED">Collected</option>-->
<!--                            <option :value="bibCollectedStatuses.NOT_COLLECTED">Not Collected</option>-->
<!--                        </select>-->
<!--                        <label>-->
<!--                            <input type="radio"-->
<!--                                   id="outdoorqerqeqewfew"-->
<!--                                   :key="bibCollectedStatuses.ALL"-->
<!--                                   class="browser-default"-->
<!--                                   :value="bibCollectedStatuses.ALL"-->
<!--                                   v-model="state.status">-->
<!--                            <span>All</span>-->
<!--                            <input type="radio"-->
<!--                                   id="outdoorqerqeqeewfew"-->
<!--                                   :key="bibCollectedStatuses.COLLECTED"-->
<!--                                   class="browser-default"-->
<!--                                   :value="bibCollectedStatuses.COLLECTED"-->
<!--                                   v-model="state.status">-->
<!--                            <span>Collected</span>-->
<!--                        </label>-->
                        <div v-for="(option) in arrivalOptions" class="col s12 m4 l2 ">
                            <label>
                                <input type="radio"
                                       id="outdoorqerqeqewfew"
                                       :key="option.id"
                                       class="browser-default"
                                       :value="option.id"
                                       v-model="state.status">
                                <span v-text="option.label"></span>
                            </label>
                        </div>

                    </div>

                </div>

                <div class="row">
                    <div class="col s12 m12 l12">
                        <div class="right">
                            <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                            <button class="btn waves-effect waves red" :disabled="isLoading" v-on:click="reset">Reset</button>
                            <button class="btn waves-effect waves green" :disabled="isLoading" v-on:click="search">Search</button>
                        </div>
                    </div>
                </div>
            </div>

        </CollapseSection>


        <div class="row" v-if="athleteCheckin.length === 0">
            <div class="col s12 m12 l12">
                <div class="e4s-section-padding-separator"></div>
<!--                <div v-text="message"></div>-->
                No results found.
                <div class="e4s-section-padding-separator"></div>
            </div>
        </div>

        <div v-for="athlete in athleteCheckin" class="checkin--athlete">
<!--            <CheckinUser :athlete-checkin="athlete" :disable-bib-collected="true"></CheckinUser>-->
<!--            <WhoishereUser :athlete-checkin="athlete" :competition-service="competitionService"></WhoishereUser>-->
            <WhoishereUserState :athlete-checkin="athlete"
                                :is-loading="isLoading"
                                :is-organiser="false"
                                :checkin-comp-summary="checkinSummary">
            </WhoishereUserState>
        </div>


    </div>
</template>

<script lang="ts">

    import Component from "vue-class-component";
    import Vue from "vue";
    import {format} from "date-fns";
    import {mapGetters, mapState} from "vuex";
    import { CompetitionService } from "../../competiton-service";
    import { CONFIG_STORE_CONST } from "../../../config/config-store";
    import {ICheckinAthlete, ICheckinCompSummary, ICheckInStoreState} from "../checkin-models";
    import {BIB_COLLECTED_STATUS, IGetCheckinSearchParams} from "../checkin-data";
    import {CHECKIN_STORE_CONST} from "../checkin-store";
    import { ValidationController } from "../../../validation/validation-controller";
    import CollapseSection from "../../../common/ui/collapse/collapse-section.vue";
    import FieldHelp from "../../../common/ui/field/field-help/field-help.vue";
    import SimpleObjectDropDown from "../../../common/ui/simple-object-drop-down.vue";
    import {IEventGroup} from "../../../builder/form/event-group/event-group-models";
    import { EventGroupService } from "../../../builder/form/event-group/event-group-service";
    import WhoishereUser from "./whoishere-user.vue";
    import CheckinUser from "../checkin-organiser/checkin-user.vue"
    import {IEntity} from "../../../config/config-app-models"
    import {ConfigService} from "../../../config/config-service"
    import WhoishereUserState from "./whoishere-user-state.vue"

    const competitionService: CompetitionService = new CompetitionService();

    @Component({
        name: "checkin-organiser",
        components: {
          WhoishereUserState,
            CheckinUser,
            WhoishereUser,
            CollapseSection,
            FieldHelp,
            SimpleObjectDropDown
        },
        computed: {
            ...mapGetters(
                {
                    isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
                    getHeaderTitle: CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_GETTERS_TITLE_HEADER
                }
            ),
            ...mapState(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME, {
                checkinSummary: (state: ICheckInStoreState) => state.checkinSummary,
                athleteCheckin: (state: ICheckInStoreState) => state.athleteCheckin,
                isLoading: (state: ICheckInStoreState) => state.isLoading,
                message: (state: ICheckInStoreState) => state.message
            })
        }
    })
    export default class WhoIsHere extends Vue {
        public readonly checkinSummary: ICheckinCompSummary;
        public readonly athleteCheckin: ICheckinAthlete[];
        public readonly isLoading: boolean;
        public readonly message: string;

        // public competition: ICompetitionInfo = competitionService.factoryCompetitionInfo();
        public competitionService: CompetitionService = competitionService;

        public bibCollectedStatuses = {
            ALL: BIB_COLLECTED_STATUS.ALL,
            COLLECTED: BIB_COLLECTED_STATUS.COLLECTED,
            NOT_COLLECTED: BIB_COLLECTED_STATUS.NOT_COLLECTED
        };

        public state = {
            compId: 0,
            firstName: "",
            surName: "",
            club: "",
            date: "",
            event: "",
            status: BIB_COLLECTED_STATUS.ALL
        };
        public PREFIX = Math.random().toString(36).substring(2);
        public validationController: ValidationController = new ValidationController();
        public configService: ConfigService = new ConfigService();
        public entity: IEntity = this.configService.factoryEntity();

        public created() {
            this.state.compId = isNaN(Number(this.$route.params.id)) ? 0 : parseInt(this.$route.params.id, 0);
            console.log("WhoIsHere.created() this.state.compId: " + this.state.compId);

            //  reset "grid"
            this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);

            if (this.state.compId > 0) {
                // new CompetitionData().getCompetitionsByid(this.state.compId)
                //     .then((response) => {
                //         console.log("WhoIsHere.created() comp", response.data);
                //         this.competition = {
                //             ...this.competition
                //         };
                //     });

                this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" +
                    CHECKIN_STORE_CONST.CHECKIN_ACTIONS_GET_SUMMARY, this.state.compId)
                    .then( () => {
                        this.state.date = this.checkinSummary.dates[0];
                    })
            }

            this.state.date = format(new Date(), "YYYY-MM-DD");
        }

        public getState() {
            return {
                compId: 0,
                firstName: "",
                surName: "",
                club: "",
                date: "",
                event: "",
                status: BIB_COLLECTED_STATUS.ALL
            };
        }

        public reset() {
            const stateNew = {
                ...this.getState(),
                compId: this.state.compId
            };
            stateNew.date = this.checkinSummary.dates[0];
            this.state = stateNew;
            this.$store.commit(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_MUTATIONS_RESET_CHECKIN_ATHLETES);
        }

        public get arrivalOptions() {
            return [
                {
                    id: this.bibCollectedStatuses.ALL,
                    label: "All"
                },
                {
                    id: this.bibCollectedStatuses.COLLECTED,
                    label: "Collected bib"
                },
                {
                    id: this.bibCollectedStatuses.NOT_COLLECTED,
                    label: "Not collected bib"
                }
            ];
        }

        public search() {
            const searchPayload: IGetCheckinSearchParams = {
                compId: this.state.compId,
                isoDate: this.state.date,
                firstName: this.state.firstName,
                lastName: this.state.surName,
                club: this.state.club,
                entity: this.entity,
                dob: "",
                urn: "",
                bibNo: "",
                eventName: this.state.event.replace("ALL", ""),
                pageNumber: 1,
                pageSize: 100,
                collected: this.state.status,
                nonce: "",
                isOrganiser: false,
                whois: true
            };
            this.$store.dispatch(CHECKIN_STORE_CONST.CHECKIN_CONST_MODULE_NAME + "/" + CHECKIN_STORE_CONST.CHECKIN_ACTIONS_SEARCH, searchPayload);
        }

        public get getEventGroupDefault() {
            return {...new EventGroupService().factoryEventGroup(), id: 1234500000, name: "ALL"};
        }

        public get getEventGroups(): IEventGroup[] {
            //  TODO id weird
            return [ this.getEventGroupDefault, ...this.checkinSummary.eventGroups];
        }
    }
</script>
