<template>
  <div>
    <LoadingSpinnerV2
      v-if="
        resultsImportController.state.isLoadingGlobal ||
        resultsImportController.state.isLoading
      "
    />

    <div
      class="e4s-card e4s-card--generic"
      v-if="resultsImportController.isReady.value"
    >
      <div class="e4s-flex-row">
        <h2>
          Import result from TimeTronics to
          <span
            v-text="
              resultsImportController.state.targetComp.builderCompetition.name
            "
          ></span>
        </h2>
      </div>

      <FormGenericFieldGridV2>
        <template slot="content">
          <div class="e4s-flex-row e4s-gap--standard">
            <FormGenericInputNumberV2
              form-label="Source E4S Competition"
              v-model="resultsImportController.state.sourceComp.id"
            />
            <FormGenericInputTextV2
              style="flex-grow: 1"
              form-label="Name"
              :is-disabled="true"
              :value="resultsImportController.getSourceCompDisplayName.value"
            >
              <PrimaryLink
                slot="after-label"
                link-text="View"
                :link="
                  '#/show-entry/' + resultsImportController.state.sourceComp.id
                "
              ></PrimaryLink>
            </FormGenericInputTextV2>
          </div>

          <div class="e4s-flex-row e4s-gap--standard">
            <FormGenericInputNumberV2
              form-label="Target E4S Competition"
              v-model="resultsImportController.state.targetComp.id"
              :is-disabled="true"
            />
            <FormGenericInputTextV2
              style="flex-grow: 1"
              form-label="Name"
              :is-disabled="true"
              :value="resultsImportController.getTargetCompDisplayName.value"
            >
              <PrimaryLink
                slot="after-label"
                link-text="View"
                :link="
                  '#/show-entry/' + resultsImportController.state.targetComp.id
                "
              ></PrimaryLink>
            </FormGenericInputTextV2>
          </div>
        </template>
      </FormGenericFieldGridV2>

      <FormGenericFieldGridV2>
        <template slot="content">
          <FormGenericInputTemplateV2 form-label="TimeTronics results url">
            <div
              slot="after-label"
              class="
                e4s-flex-row e4s-justify-flex-row-vert-center-xxx
                e4s-gap--standard
              "
            >
              <SelectFileFromDisk
                v-if="false"
                v-on:input="selectTimeTronicsFile"
                class="e4s-button--slim"
              />

              <div class="e4s-flex-row e4s-gap--large">
                <FieldRadioV2
                  :option-value="false"
                  v-model.number="resultsImportController.state.useLocalServer"
                  label="Use E4S Server"
                />
                <FieldRadioV2
                  :option-value="true"
                  v-model.number="resultsImportController.state.useLocalServer"
                  label="Use local server"
                />
              </div>

              <!--              <span-->
              <!--                v-text="-->
              <!--                  'Loaded: ' +-->
              <!--                  (resultsImportController.isTimeTronicsCompLoaded.value-->
              <!--                    ? resultsImportController.getTimeTronicsDisplayName.value-->
              <!--                    : 'No')-->
              <!--                "-->
              <!--              ></span>-->
            </div>

            <FieldTextV2
              slot="field"
              v-model="
                resultsImportController.state.timeTronicsCompetitionServerUrl
              "
            />
          </FormGenericInputTemplateV2>
        </template>
      </FormGenericFieldGridV2>

      <div class="e4s-flex-column e4s-gap--standard">
        <FormGenericInputTemplateV2 form-label="Athletes will compete as:">
          <div class="e4s-flex-row e4s-gap--standard" slot="field">
            <EntitySelect
              style="width: 300px"
              :please-select="true"
              :user-entities="resultsImportController.state.userEntities"
              v-on:input="
                resultsImportController.importServiceController.setEnity
              "
            />

            <ButtonGenericV2
              text="Load"
              :disabled="resultsImportController.state.userEtity.id === 0"
              @click="
                resultsImportController.importServiceController.getAllData()
              "
            />
          </div>
        </FormGenericInputTemplateV2>
      </div>
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <div v-if="resultsImportController.isReady.value">
      <div
        class="e4s-flex-column"
        v-if="resultsImportController.state.userEtity.id > 0"
      >
        <div class="e4s-flex-row e4s-justify-flex-space-between">
          <div class="e4s-flex-row e4s-gap--standard">
            <InputDebounce
              :value="resultsImportController.state.filterTerm"
              @input="resultsImportController.filterTermChanged"
            />

            <FormGenericInputTemplateV2 form-label="Only Finals">
              <FieldCheckboxV2
                slot="field"
                style="align-self: flex-start"
                :value="resultsImportController.state.showOnlyFinals"
                @input="resultsImportController.showFinalsOnlyChanged"
              />
            </FormGenericInputTemplateV2>
          </div>

          <FormGenericInputTemplateV2 form-label="Sort By">
            <select
              slot="field"
              class="browser-default e4s-input-field e4s-input-field--primary"
              style="width: 300px"
              v-model="resultsImportController.state.timeTronicsEventsGroupBy"
              v-on:change="
                resultsImportController.importServiceController.loadData()
              "
            >
              <option value="EVENT_TYPE">Event Type</option>
              <option value="SEQNO">Event Order</option>
            </select>
          </FormGenericInputTemplateV2>
        </div>
      </div>
    </div>

    <div
      class="e4s-flex-column"
      :class="
        resultsImportController.state.timeTronicsEventsGroupBy === 'SEQNO'
          ? 'e4s-gap--standard'
          : 'e4s-gap--large'
      "
      v-if="resultsImportController.state.timeTronicsCompetition"
    >
      <div
        class="results-import--event-grouping"
        v-for="(group, prop) in resultsImportController.state
          .timeTronicsEventsGroupedFilteredByEventName"
      >
        <div
          class="
            e4s-flex-row
            results-import--event-grouping-header
            e4s-header--400
          "
          v-if="
            resultsImportController.state.timeTronicsEventsGroupBy ===
            'EVENT_TYPE'
          "
        >
          {{ prop }}
        </div>

        <div v-for="timeTronicsEvent in group" :key="timeTronicsEvent.id">
          <!--                <AccordionV3 title="Test Title">-->
          <!--                  <div slot="content">-->
          <!--                    qwdfwe fwef wef ewfwefwefwfgwef wef wef we fwe f wef wef we-->
          <!--                    fwe f-->
          <!--                  </div>-->
          <!--                </AccordionV3>-->

          <ResultsImportEvent
            class="results-import--event"
            :time-tronics-event="timeTronicsEvent"
            :show-source-event-group-summaries="
              resultsImportController.state.sourceComp.builderCompetition.id > 0
            "
            :source-event-group-summaries="
              resultsImportController.state.sourceComp.eventGroupSummaries
            "
            :target-event-group-summaries="
              resultsImportController.state.targetComp.eventGroupSummaries
            "
            :time-tronics-event-list="
              resultsImportController.state.timeTronics.timeTronicsEventMap[
                timeTronicsEvent.id
              ]
            "
            v-on:getResults="
              resultsImportController.importServiceController
                .getDataForEventList;
            "
            @onExpanded="
              resultsImportController.importServiceController
                .onResultsImportEventExpanded;
            "
          />
        </div>
      </div>
    </div>

    <div class="e4s-vertical-spacer--large"></div>

    <div
      class="e4s-card e4s-card--generic"
      style="background: var(--grey-50)"
      v-if="resultsImportController.isReady.value"
    >
      <div class="e4s-header--500">Admin Debug Section</div>

      <div class="e4s-flex-row e4s-gap--standard">
        <FormGenericInputTemplateV2 form-label="Display Debug Data">
          <select
            slot="field"
            class="browser-default e4s-input-field e4s-input-field--primary"
            style="width: 300px"
            v-model="uiDebug.showTargetEntries"
          >
            <option value="EVENT_GROUP_URN">Event Group\Urn</option>
            <option value="SEQ_EVENT_KEY">TT Event Order</option>
            <option value="ENTRIES">Event Group Entries</option>
            <option value="PAYEES">Payees</option>
          </select>
        </FormGenericInputTemplateV2>

        <!--        <FormGenericInputTemplateV2-->
        <!--          form-label="Show TargetEntries by Event Group and Urn"-->
        <!--        >-->
        <!--          <FieldCheckboxV2-->
        <!--            slot="field"-->
        <!--            style="align-self: flex-start"-->
        <!--            v-model="uiDebug.showTargetEntries"-->
        <!--          />-->
        <!--        </FormGenericInputTemplateV2>-->
      </div>

      <div v-if="uiDebug.showTargetEntries === 'EVENT_GROUP_URN'">
        {{ resultsImportController.state.targetComp.entriesEventGroupUrnMap }}
      </div>

      <div v-if="uiDebug.showTargetEntries === 'SEQ_EVENT_KEY'">
        {{
          resultsImportController.state.targetComp
            .entriesE4sResultImportSeqEventKey
        }}
      </div>

      <div v-if="uiDebug.showTargetEntries === 'ENTRIES'">
        {{ resultsImportController.state.targetComp.entries }}
      </div>

      <div v-if="uiDebug.showTargetEntries === 'PAYEES'">
        {{ resultsImportController.state.e4s.athletesPayees }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { useResultsImportController } from "./useResultsImportController";
import ResultsImportEvent from "./ResultsImportEvent.vue";

import { TimeTronicsEventsGroups } from "./results-import-models";
import ButtonE4s from "../../common/ui/buttons/button-e4s.vue";
import EntitySelect from "../../config/entity/entity-select.vue";
import { useConfigStore } from "../../config/useConfigStore";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import SelectFileFromDisk from "../../common/files/SelectFileFromDisk.vue";
import FormGenericFieldGridV2 from "../../common/ui/layoutV2/form/form-generic-field-grid-v2.vue";
import FormGenericInputTextV2 from "../../common/ui/layoutV2/form/form-generic--input-text-v2.vue";
import FormGenericInputNumberV2 from "../../common/ui/layoutV2/form/form-generic--input-number-v2.vue";
import FormGenericInputTemplateV2 from "../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import FieldTextV2 from "../../common/ui/layoutV2/fields/field-text-v2.vue";
import FieldCheckboxV2 from "../../common/ui/layoutV2/fields/field-checkbox-v2.vue";
import InputDebounce from "../../common/ui/field/input-debounce.vue";
import LoadingSpinnerV2 from "../../common/ui/loading-spinner-v2.vue";
import AccordionV3 from "../../common/ui/layoutV2/accordion/AccordionV3.vue";
import PrimaryLink from "../../common/ui/layoutV2/href/PrimaryLink.vue";
import FieldRadioV2 from "../../common/ui/layoutV2/fields/field-radio-v2.vue";

export default defineComponent({
  name: "results-import",
  components: {
    FieldRadioV2,
    PrimaryLink,
    AccordionV3,
    LoadingSpinnerV2,
    InputDebounce,
    FieldCheckboxV2,
    FieldTextV2,
    FormGenericInputTemplateV2,
    FormGenericInputNumberV2,
    FormGenericInputTextV2,
    FormGenericFieldGridV2,
    SelectFileFromDisk,
    ButtonGenericV2,
    EntitySelect,
    ButtonE4s,
    ResultsImportEvent,
  },
  props: {
    targetCompId: {
      type: Number,
      default: 0,
    },
    sourceCompId: {
      type: Number,
      default: 0,
    },
  },
  setup(
    props: { targetCompId: number; sourceCompId: number },
    context: SetupContext
  ) {
    const resultsImportController = useResultsImportController();
    const timeTronicsEventsGroups = TimeTronicsEventsGroups;
    const configStore = useConfigStore();

    const uiDebug = reactive({
      showTargetEntries: "" as
        | ""
        | "EVENT_GROUP_URN"
        | "SEQ_EVENT_KEY"
        | "ENTRIES"
        | "PAYEES",
    });

    init();

    function init() {
      resultsImportController.importServiceController.init(
        props.targetCompId,
        props.sourceCompId
      );
    }

    watch(
      () => props.targetCompId,
      (newValue: number) => {
        init();
      }
    );

    function selectTimeTronicsFile(data: File) {
      console.log("ResultsImport.selectTimeTronicsFile", data);
      resultsImportController.importServiceController.gotTimeTronicsFileFromDisk(
        data
      );
    }

    // function showFinalsOnlyChanged(showOnlyFinals: boolean) {
    //   console.log(
    //     "ResultsImport.showFinalsOnlyChanged showOnlyFinals: " + showOnlyFinals
    //   );
    //   resultsImportController.importServiceController.showFinalsOnlyChanged(
    //     showOnlyFinals
    //   );
    // }

    return {
      resultsImportController,
      timeTronicsEventsGroups,
      configStore,
      selectTimeTronicsFile,
      uiDebug,
    };
  },
});
</script>

<style scoped>
.results-import--event {
  margin-top: var(--e4s-gap--small);
}
</style>
