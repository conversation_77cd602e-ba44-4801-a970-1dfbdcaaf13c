<template>
  <div class="row">
    <div class="col s12 m12 l12">
      <div
        class="e4s-flex-row e4s-gap--standard e4s-justify-flex-row-vert-center"
      >
        <ButtonGenericV2
          text="Back"
          v-on:click="toSchedule"
          class="e4s-button--auto"
          v-if="showBackButton"
        />
        <ButtonGenericV2
          v-if="isAdmin || hasResultsPermissionForComp"
          text="Card"
          v-on:click="goToSeeding"
          class="e4s-button--auto"
        />

        <ButtonGenericV2
          v-if="(isAdmin || hasResultsPermissionForComp) && showAddRace"
          :text="isTrackEvent ? 'Add Race' : 'Add Heat'"
          v-on:click="addHeat"
          class="e4s-button--auto"
        />

        <div class="e4s-flex-row e4s-flex-wrap">
          <div
            v-for="heat in resultEvent.heats"
            :key="heat.heatNo"
            class="r4s-ranking--heat-link"
          >
            <span
              v-if="isHeatSelected(heat.heatNo)"
              v-text="getHeatName(heat)"
            ></span>
            <PrimaryLink
              v-if="!isHeatSelected(heat.heatNo)"
              :link-text="getHeatName(heat)"
              @onClick="changeHeat(heat.heatNo)"
            />
          </div>

          <div v-if="showOverall" class="r4s-ranking--heat-link">
            <span v-if="isHeatSelected(-1)"> Overall </span>
            <PrimaryLink
              v-if="!isHeatSelected(-1)"
              link-text="Overall"
              @onClick="changeHeat(-1)"
              :key="-1"
            />
          </div>
        </div>

        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IResultEvent, IResultHeat } from "../results-models";
import { IR4sCompSchedule } from "../../scoreboard/rs4/rs4-scoreboard-models";
import { ResultsService } from "../results-service";
import { Rs4Service } from "../../scoreboard/rs4/rs4-service";
import { mapGetters, mapState } from "vuex";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../config/config-store";
import { IConfigApp } from "../../../config/config-app-models";
import { ConfigService } from "../../../config/config-service";
import ButtonGenericV2 from "../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";

const resultsService: ResultsService = new ResultsService();
const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "result-event-heat-links",
  components: { PrimaryLink, ButtonGenericV2 },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class ResultEventHeatLinks extends Vue {
  public readonly isAdmin!: boolean;
  public configApp!: IConfigApp;

  @Prop({
    default: () => {
      return resultsService.factoryResultEvent();
    },
  })
  public readonly resultEvent: IResultEvent;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: false,
  })
  public readonly showAddRow!: boolean;

  @Prop({
    default: false,
  })
  public readonly showAddRace!: boolean;

  @Prop({
    default: true,
  })
  public readonly showBackButton!: boolean;

  @Prop({
    default: 1,
  })
  public readonly defaultHeatNumberToDisplay!: number;

  @Prop({
    default: true,
  })
  public readonly showOverall!: boolean;

  @Prop({
    default: false,
  })
  public readonly isEmbedded!: boolean;

  public heatNumberToDisplay: number = 1;
  public configService: ConfigService = new ConfigService();

  public created() {
    this.heatNumberToDisplay = this.defaultHeatNumberToDisplay;
  }

  @Watch("defaultHeatNumberToDisplay")
  public onDefaultHeatNumberToDisplay(heatNumber: number) {
    this.heatNumberToDisplay = heatNumber;
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.resultEvent.comp.id
    );
  }

  public isHeatSelected(heatNumber: number): boolean {
    return heatNumber === this.heatNumberToDisplay;
  }

  public changeHeat(heatNumber: number) {
    if (this.heatNumberToDisplay === heatNumber) {
      //  User clicking same link as displayed.
      return;
    }
    this.heatNumberToDisplay = heatNumber;
    this.$emit("changeHeat", heatNumber);
  }

  public toSchedule() {
    this.$emit("toSchedule");
  }

  public getHeatName(resultHeat: IResultHeat): string {
    return resultsService.getHeatName(resultHeat);
  }

  public addHeat() {
    this.$emit("addHeat");
  }
  public goToSeeding() {
    this.$emit("goToSeeding");
  }

  public get isTrackEvent() {
    return resultsService.isTrackEvent(this.resultEvent.eventGroup.type);
  }
}
</script>

<style scoped>
.r4s-ranking--heat-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
  //font-weight: 500;
}

.r4s-ranking--heat-link-selected {
  color: black;
}
</style>
