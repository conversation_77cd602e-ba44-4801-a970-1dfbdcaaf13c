import {
  IPhotoFinishControllerState,
  IPhotoFinishSocketMessage, IPhotoFinishSocketMessagePayload,
} from "./usePhotoFinish"
import { BuilderService } from "../../../../builder/builder-service";

export function factoryPhotoFinishControllerState(): IPhotoFinishControllerState {
  return {
    isLoading: false,
    builderCompetition: new BuilderService().factoryGetBuilder({}),
    domain: "",
    targetDirectoryName: "",
    targetDirectoryHandle: null,
    latestPhotoFinishPayload: {
      file: "",
      data: "",
    },
    manualPhotoFinishPayload: {
      file: "",
      data: "",
    },
    commonFiles: []
  };
}

export function isSocketMessageOk(
  message: IPhotoFinishSocketMessage<IPhotoFinishSocketMessagePayload>,
  matchData: { compId: number; domain: string }
): boolean {
  if (message.action !== "pf_file") {
    return false;
  }
  if (message.comp && message.domain) {

    const matchDomain = matchData.domain.replace("https://", "")

    return (
      message.comp.id === matchData.compId &&
      message.domain === matchDomain
    );
  }
  return false;
}
