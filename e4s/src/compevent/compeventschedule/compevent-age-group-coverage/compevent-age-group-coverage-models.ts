import { IsoDate } from "../../../common/common-models";
import { IAgeGroupOption } from "../../../agegroup/agegroup-models";

export interface ICompEventAgeGroupBase {
  id: number;
  name: string;
  toDate: IsoDate;
  fromDate: IsoDate;
}

//  TODO this model needs to go, just need <PERSON> to fix it.
export interface ICompEventAgeGroupCoverageModel {
  compDate: IsoDate;
  agid: number;

  todate: IsoDate;
  fromdate: IsoDate;
  id: number;
  Name: string;

  MaxAge: number;
  AtDay: number;
  AtMonth: number;
  year: number;

  minAge: number;
  minAtDay: number | null;
  minAtMonth: number | null;
  minYear: number | null;

  keyName: string; //  E.g. Masters
  options: IAgeGroupOption[];
  shortName: string; //  E.g. Masters
}

export interface IAgeGroupHtmlCellObject {
  counter: number;
  colSpan: number;
  title: string;
  year: number;
  content: string;
  removeOnRender: boolean;
  hasAgeGroup: boolean;
  ageGroupId: number;
  dateOfBirthMatch: boolean;
  dateOfBirthYearMatch: boolean;
  diffDaysToPrevious: number;
  isDefaultAgeGroup: boolean;
}
