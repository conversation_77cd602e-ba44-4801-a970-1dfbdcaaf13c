<template>
  <tr>
    <td
      v-for="(cell, index) in htmlCellObjects"
      v-html="cell.content"
      :title="cell.title"
      :key="cell.year"
      :colspan="cell.colSpan"
      :class="getCss(cell)"
      class="compevent-age-group-coverage-row--cell"
      v-on:click="ageCellClicked(cell.ageGroupId)"
    ></td>
  </tr>
  <!--  </div>-->
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IAgeGroupCompCoverageModel } from "../../../agegroup/agegroup-models";
import { IsoDate } from "../../../common/common-models";
import { CompeventAgeGroupCoverageService } from "./compevent-age-group-coverage-service";
import { IAgeGroupHtmlCellObject } from "./compevent-age-group-coverage-models";
import { differenceInDays, format, parse } from "date-fns";
import { AgeGroupService } from "../../../agegroup/agegroup-service";
import { AO_CODE } from "../../../common/ui/athletic-org/athletic-org-models";
import {
  getAgeInYearsMonthsDaysObject,
  IAgeInYearsMonthsDays,
} from "../../../common/common-service-utils";

const compeventAgeGroupCoverageService = new CompeventAgeGroupCoverageService();
const ageGroupService = new AgeGroupService();

@Component({
  name: "compevent-age-group-coverage-row",
  components: {},
})
export default class CompeventAgeGroupCoverageRow extends Vue {
  @Prop({
    default: false,
  })
  public readonly debug: boolean;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroupCompCoverageModels: IAgeGroupCompCoverageModel[];

  @Prop({
    default: () => {
      return { min: 0, max: 100 };
    },
  })
  public readonly yearsToDisplay: { min: number; max: number };

  @Prop({
    default: "",
  })
  public readonly dateOfBirth: IsoDate;

  @Prop({
    default: "",
  })
  public readonly compDate: IsoDate;

  @Prop({
    default: false,
  })
  public readonly isAdmin: boolean;

  @Prop({
    default: false,
  })
  public readonly isHeader: boolean;

  @Prop({
    required: true,
  })
  public readonly aoCode: AO_CODE;

  public readonly yearsToCalc: { min: number; max: number } = {
    min: 0,
    max: 100,
  };

  public yearBlocks: unknown[] = [];
  public yearBlocksDisplay: unknown[] = [];
  public htmlCellObjects: IAgeGroupHtmlCellObject[] = [];
  public oneBlockwidthPercent = 0;

  public dobAgeInYearsMonthsDays: IAgeInYearsMonthsDays = {
    years: 0,
    months: 0,
    days: 0,
  };

  @Watch("yearsToDisplay", { immediate: true, deep: true })
  public onYearsToDisplayChanged() {
    this.setUp();
  }

  @Watch("ageGroupCompCoverageModels", { immediate: true })
  public onAgeGroupCompCoverageModelsChanged() {
    this.setUp();
  }

  @Watch("dateOfBirth", { immediate: true })
  public onDateOfBirthChanged(newValue: IsoDate, oldValue: IsoDate) {
    if (!newValue) {
      return;
    }
    if (this.debug) {
      console.log("CompEventAgeGroupCoverageRow: onDateOfBirthChanged");
    }
    if (newValue.length === 10) {
      this.setUp();
    }
    if (newValue.length === 0 && oldValue.length === 10) {
      this.setUp();
    }
  }

  public setUp() {
    const yearBlocks: unknown[] = [];
    // for (let i = this.yearsToDisplay.max; i >= this.yearsToDisplay.min; i--) {
    //   yearBlocks.push(i);
    // }

    if (this.debug) {
      console.log("CompEventAgeGroupCoverageRow: setUp");
    }

    if (this.compDate.length > 0) {
      this.dobAgeInYearsMonthsDays = getAgeInYearsMonthsDaysObject(
        this.dateOfBirth,
        this.compDate
      );
    }

    for (let i = this.yearsToCalc.min; i <= this.yearsToCalc.max; i++) {
      yearBlocks.push(i);
    }

    this.ageGroupCompCoverageModels.forEach((ageGroup) => {
      // const spread = ageGroup.maxAge - ageGroup.minAge;
      for (let i = ageGroup.maxAge; i >= ageGroup.minAge; i--) {
        if (i <= this.yearsToCalc.max && i >= this.yearsToCalc.min) {
          yearBlocks[i] = ageGroup.keyName;
        }
      }
    });

    this.yearBlocks = yearBlocks;
    this.oneBlockwidthPercent =
      Math.floor(1 / this.getYearRangeCountToDisplay) * 100;

    this.getHtmlAsTable();
  }

  public getTitle(
    ageGroupCompCoverageModel: IAgeGroupCompCoverageModel
  ): string {
    return (
      ageGroupCompCoverageModel.name +
      " [" +
      ageGroupCompCoverageModel.minAge +
      "-" +
      ageGroupCompCoverageModel.maxAge +
      ", " +
      format(parse(ageGroupCompCoverageModel.fromDate), "Do MMM YYYY") +
      " to " +
      format(parse(ageGroupCompCoverageModel.toDate), "Do MMM YYYY") +
      "]" +
      (this.isAdmin ? " (" + ageGroupCompCoverageModel.id + ")" : "")
    );
  }

  public get getYearWidth(): number {
    const spreadToDisplay =
      this.yearsToDisplay.max - this.yearsToDisplay.min + 1;
    let val = 100 / spreadToDisplay;
    val = Math.round(val * 10) / 10;
    const recalcBack = spreadToDisplay * val;
    if (recalcBack * spreadToDisplay > 100) {
      val = Math.trunc(val);
    }
    return val;
  }

  // public getWidth(
  //   ageGroupCompCoverageModel: IAgeGroupCompCoverageModel
  // ): string {
  //   const width =
  //     ageGroupCompCoverageModel.maxAge - ageGroupCompCoverageModel.minAge + 1;
  //   return width + "%";
  // }

  public get getYearRangeCountToDisplay(): number {
    return this.yearsToCalc.max - this.yearsToCalc.min;
  }

  public getHtmlAsTable() {
    // if (this.debug) {
    //   console.log("getHtmlAsTable start...");
    // }
    let html: string[] = [];
    const yearBlocks = [];
    for (let i = 0; i <= this.yearBlocks.length; i++) {
      yearBlocks.push(i);
    }

    const htmlCell: string[] = yearBlocks.map((block) => {
      return (
        "<td class='compevent-age-group-coverage-row--cell-xxx'>" +
        block +
        "</td>"
      );
    });

    const htmlCellObjects: IAgeGroupHtmlCellObject[] = yearBlocks.map(
      (block, index) => {
        return {
          counter: index,
          colSpan: 1,
          title: block.toString(),
          content: block.toString(),
          year: block,
          removeOnRender: false,
          hasAgeGroup: false,
          ageGroupId: 0,
          dateOfBirthMatch: false,
          dateOfBirthYearMatch: this.dobAgeInYearsMonthsDays.years === index,
          diffDaysToPrevious: 0,
          isDefaultAgeGroup: true,
        };
      }
    );

    // let previousToDate: IsoDate | "" = "";
    let prevAgeGroup: IAgeGroupCompCoverageModel | null = null;

    this.ageGroupCompCoverageModels.forEach((ageGroup) => {
      // if (this.debug) {
      //   console.log("getHtmlAsTable forEach...", ageGroup);
      // }

      const min =
        ageGroup.minAge < this.yearsToDisplay.min
          ? this.yearsToDisplay.min
          : ageGroup.minAge;
      const max =
        ageGroup.maxAge > this.yearsToDisplay.max
          ? this.yearsToDisplay.max
          : ageGroup.maxAge;
      const spread = max - min + 1;

      for (let i = min; i <= max; i++) {
        if (i === min) {
          htmlCell[i] =
            "<td v-on:click='ageClicked(ageGroup)'  colspan='" +
            spread +
            "' class='compevent-age-group-coverage-row--td-full'><div title='" +
            this.getTitle(ageGroup) +
            "'>" +
            (ageGroup.shortName && ageGroup.shortName.length > 0
              ? ageGroup.shortName
              : ageGroup.keyName) +
            "</div></td>";

          // (ageGroup.shortName.length > 0 ? ageGroup.shortName : ageGroup.keyName) +
          htmlCellObjects[i].colSpan = spread;
          htmlCellObjects[i].title = this.getTitle(ageGroup);
          htmlCellObjects[i].content = "<td>" + ageGroup.name + "</td>";
          htmlCellObjects[i].hasAgeGroup = true;
          htmlCellObjects[i].ageGroupId = ageGroup.id;
          htmlCellObjects[i].isDefaultAgeGroup =
            ageGroupService.isDefaultAgeGroup(ageGroup, this.aoCode);

          const dateOfBirthMatch =
            this.dateOfBirth.length > 0
              ? compeventAgeGroupCoverageService.isDateContainedIn(
                  ageGroup,
                  this.dateOfBirth
                )
              : false;

          htmlCellObjects[i].dateOfBirthMatch = dateOfBirthMatch;

          htmlCellObjects[i].dateOfBirthYearMatch =
            this.dobAgeInYearsMonthsDays.years === htmlCellObjects[i].counter;
          if (this.debug) {
            console.log(
              "dateOfBirthYearMatch: " +
                htmlCellObjects[i].dateOfBirthYearMatch +
                " " +
                this.dobAgeInYearsMonthsDays.years +
                " " +
                htmlCellObjects[i].counter
            );
          }

          const diffToLastAgeGroup = prevAgeGroup
            ? differenceInDays(ageGroup.fromDate, prevAgeGroup.toDate)
            : 0;
          htmlCellObjects[i].diffDaysToPrevious = diffToLastAgeGroup;

          prevAgeGroup = R.clone(ageGroup);
        } else {
          htmlCell[i] = "";
          htmlCellObjects[i].content = "";
          htmlCellObjects[i].removeOnRender = true;
        }
      }
    });

    const cellsToDispplay = htmlCell.slice(
      this.yearsToDisplay.min,
      this.yearsToDisplay.max
    );

    this.htmlCellObjects = htmlCellObjects
      .slice(this.yearsToDisplay.min, this.yearsToDisplay.max)
      .filter((htmlCellObject) => {
        return !htmlCellObject.removeOnRender;
      });

    html.push(cellsToDispplay.join(""));
    return html.join("");
  }

  public getCss(ageGroupHtmlCellObject: IAgeGroupHtmlCellObject) {
    const css = [];

    if (this.isHeader) {
      css.push("compevent-age-group-coverage-row--cell-header");
      if (ageGroupHtmlCellObject.dateOfBirthYearMatch) {
        css.push("compevent-age-group-coverage-row--cell-match");
      }
      return css.join(" ");
    }

    if (ageGroupHtmlCellObject.hasAgeGroup) {
      css.push("compevent-age-group-coverage-row--cell-full");
    }

    if (ageGroupHtmlCellObject.dateOfBirthMatch) {
      css.push("compevent-age-group-coverage-row--cell-match");
    }

    if (ageGroupHtmlCellObject.diffDaysToPrevious > 1) {
      css.push("compevent-age-group-coverage-row--cell-days-gap");
    }

    if (!ageGroupHtmlCellObject.isDefaultAgeGroup) {
      css.push("compevent-age-group-coverage-row--non-default");
    }

    return css.join(" ");
  }

  public ageCellClicked(ageGroupId: number) {
    console.log("ageCellClicked ageGroupId: " + ageGroupId);
    const ageGroup: IAgeGroupCompCoverageModel | null =
      this.ageGroupCompCoverageModels.reduce<IAgeGroupCompCoverageModel | null>(
        (accum, model) => {
          if (ageGroupId === model.id) {
            accum = model;
          }
          return accum;
        },
        null
      );

    if (ageGroup) {
      this.$emit("ageGroupClicked", R.clone(ageGroup));
    }
  }
}
</script>

<style>
.compevent-age-group-coverage-row--cell {
  background-color: #f5f2f2;
  padding: 3px;
  margin: 0;
  border: 1px solid darkgrey;
  color: grey;
}

.compevent-age-group-coverage-row--cell-full {
  background-color: #a3fda3;
  border: 1px solid darkgrey;
  color: black;
  font-weight: 500;
}

.compevent-age-group-coverage-row--cell-match {
  background-color: #62fa62;
  border: 2px solid #000cff;
}

.compevent-age-group-coverage-row--cell-days-gap {
  border-right: 5px solid red;
}

.compevent-age-group-coverage-row--cell-header {
  background-color: #cdcccc;
  color: black;
}

.compevent-age-group-coverage-row--non-default {
  color: var(--e4s-info-section--warn__text-color);
}
</style>
