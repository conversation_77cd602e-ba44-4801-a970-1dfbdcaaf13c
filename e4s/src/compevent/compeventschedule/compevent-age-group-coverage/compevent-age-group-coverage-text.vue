<template>
  <div>
    <div v-for="ageGroup in getAgeGroups">
      <div class="row">

        <div class="col">
          <div v-text="ageGroup.shortName"></div>
        </div>

        <div class="col">
          <div v-text="'F: ' + ageGroup.toDate"></div>
        </div>

        <div class="col">
          <div v-text="'F: ' + ageGroup.fromDate"></div>
        </div>

      </div>
    </div>
  </div>
</template>

<script lang="ts">
// import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {IAgeGroupCompCoverageModel} from "../../../agegroup/agegroup-models";
import {CompeventAgeGroupCoverageService} from "./compevent-age-group-coverage-service";

const compeventAgeGroupCoverageService = new CompeventAgeGroupCoverageService();

@Component({
  name: "compevent-age-group-coverage-text",
  components: {
  }
})
export default class CompeventAgeGroupCoverageText extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly ageGroupCompCoverageModels: IAgeGroupCompCoverageModel[];

  // public ageGroupCompCoverageModelsInternal: IAgeGroupCompCoverageModel[];

  @Watch("message")
  public onMessageChanged(newValue: string, oldValue: string) {
  }

  public get getAgeGroups() {
    return compeventAgeGroupCoverageService.orderAgeGroups(this.ageGroupCompCoverageModels);
  }
}
</script>
