<template>
    <e4s-modal v-if="showTogglePaidConf"
               :header-message="'Toggle Paid'"
               :button-primary-text="'Continue'"
               :isLoading="isLoading"
               v-on:closeSecondary="cancel"
               v-on:closePrimary="togglePaid">

        <div slot="body">
            <div class="row">
                <div class="col s12 m12 l12">
                    <span v-text="getToggleSubmitDialogMessage"></span>
                </div>
                <div class="col s12 m12 l12">
                    <label class="active"
                           :class="errors.has('reason') ? 'e4s-label-invalid' : ''"
                           for="reason">
                        Reason
                        <span v-show="errors.has('reason')">Required</span>
                    </label>
                    <input
                            id="reason"
                            name="reason"
                            v-validate="'required'"
                            v-model="paidMessage"
                            placeholder=""
                            class="validate">
                </div>
            </div>
        </div>

        <button slot="button-close-primary"
                class="btn xxx-btn-small btn-flat green-text e4s-bold"
                :disabled="getToggleSubmitDisabled"
                v-on:click.stop="togglePaid">
            <span>Continue</span>
        </button>

    </e4s-modal>
</template>

<script lang="ts">

    import Vue from "vue";
    import {Prop, Watch} from "vue-property-decorator";
    import {ATH_COMP_SCHED_STORE_CONST} from "../../athletecompsched/store/athletecompsched-store";
    import * as R from "ramda";
    import {ICartEvent} from "../../cart/cart-models";
    import { CartService } from "../../cart/cart-service";
    import Component from "vue-class-component";
    import E4sModal from "../../common/ui/e4s-modal.vue";

    const cartService: CartService = new CartService();

    @Component({
        name: "toggle-paid",
        components: {
            E4sModal
        }
    })
    export default class TogglePaid extends Vue {
        @Prop({
            default: () => {
                return cartService.factoryCartEvent();
            }
        }) public cartEvent: ICartEvent;
        @Prop({default: true})
        public readonly showTogglePaidConf!: boolean;

        public cartEventInternal: ICartEvent = cartService.factoryCartEvent();
        public isLoading: boolean = false;
        public paidMessage: string  = "";

        public created() {
            this.cartEventInternal = R.clone(this.cartEvent);
        }

        @Watch("cartEvent")
        public onCartEventChanged(newValue: ICartEvent) {
            this.cartEventInternal = R.clone(newValue);
        }

        public togglePaid() {
            const cartEvent = this.cartEventInternal;
            if (!cartEvent) {
                return;
            }
            this.$store.dispatch(
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME + "/" +
                ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_ACTIONS_SET_PAID,
                {
                    prodIds: [cartEvent.prodId],
                    paid: cartEvent.paid ? 0 : 1,
                    message: this.paidMessage
                }
            )
                .then(() => {
                    // this.showTogglePaidConf = false;
                    this.$emit("eventPaidToggle", R.clone(cartEvent));
                });
        }

        public get getToggleSubmitDialogMessage(): string {
            if (!this.cartEvent) {
                return "";
            }
            const togglePaidEventName: string = this.cartEvent ? this.cartEvent.Name :  "";
            return "Please enter a reason why toggling paid status of: " + togglePaidEventName;
        }

        public get getToggleSubmitDisabled(): boolean {
            return this.paidMessage.length === 0;
        }

        public cancel() {
            this.$emit("onCancel");
        }
    }

</script>
