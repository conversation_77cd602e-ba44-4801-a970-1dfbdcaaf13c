<template>
  <a :href="configVersionSwitch.getSwitchPath.value">
    <template v-if="currentUiVersion === 'v1'">
      <span style="color: #8d8d8d; padding-right: 5px">V1</span>V2
    </template>
    <template v-if="currentUiVersion === 'v2'">
      v1<span style="color: #8d8d8d; padding-left: 5px">V2</span>
    </template>
  </a>
</template>

<script lang="ts">
import { defineComponent, PropType, SetupContext } from "@vue/composition-api";
import { UiVersion } from "./config-app-models";
import { useConfigVersionSwitch } from "./useConfigVersionSwitch";
import { useConfigStore } from "./useConfigStore";

export default defineComponent({
  name: "config-version-switch-v1",
  components: {},
  props: {
    currentUiVersion: {
      type: String as PropType<UiVersion>,
      default: () => {
        const uiVersion: UiVersion = "v1";
        return uiVersion;
      },
    },
  },
  setup(props: { currentUiVersion: UiVersion }, context: SetupContext) {
    const configStore = useConfigStore();
    const configVersionSwitch = useConfigVersionSwitch(
      props.currentUiVersion,
      configStore.configApp.userInfo.user.version
    );
    return { configVersionSwitch };
  },
});
</script>
