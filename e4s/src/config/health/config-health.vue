<template functional>
    <div class="config-health">
        <div class="row">
            <div class="input-field col s6 m3 l3">
                <label class="active">
                    Entry: <span v-text="props.health.entryCheck.every"></span>
                </label>
                <span
                    :class="$options.methods.isLate(props.health.entryCheck) ? 'config-health--is-late' : ''"
                    v-text="$options.methods.lastRan(props.health.entryCheck)"
                ></span>
            </div>

            <div class="input-field col s6 m3 l3">
                <label class="active">
                    Waiting: <span v-text="props.health.waitingList.every"></span>
                </label>
                <span
                    :class="$options.methods.isLate(props.health.waitingList) ? 'config-health--is-late' : ''"
                    v-text="$options.methods.lastRan(props.health.waitingList)"
                ></span>
            </div>

            <div class="input-field col s6 m3 l3">
                <label class="active">
                    Health: <span v-text="props.health.healthMonitor.every"></span>
                </label>
                <span
                    :class="$options.methods.isLate(props.health.healthMonitor) ? 'config-health--is-late' : ''"
                    v-text="$options.methods.lastRan(props.health.healthMonitor)"
                ></span>
            </div>

            <div class="input-field col s6 m3 l3">
                <label class="active">
                    Payment: <span v-text="props.health.paymentStatus.every"></span>
                </label>
                <span
                    :class="$options.methods.isLate(props.health.paymentStatus) ? 'config-health--is-late' : ''"
                    v-text="$options.methods.lastRan(props.health.paymentStatus)"
                ></span>
            </div>

            <div class="input-field col s6 m3 l3">
                <label class="active">
                    Order: <span v-text="props.health.orderCheck.every"></span>
                </label>
                <span
                    :class="$options.methods.isLate(props.health.orderCheck) ? 'config-health--is-late' : ''"
                    v-text="$options.methods.lastRan(props.health.orderCheck)"
                ></span>
            </div>
        </div>

    </div>
</template>

<script lang="ts">

import {IConfigHealthStatus} from "../config-app-models";
import {differenceInSeconds, format, parse} from "date-fns";

export default {
    props: ["health"],
    methods: {
        isLate: (configHealthStatus: IConfigHealthStatus) => {
            const diff = differenceInSeconds(new Date(), parse(configHealthStatus.lastRun));
            return diff > configHealthStatus.every;
        },
        lastRan: (configHealthStatus: IConfigHealthStatus) => {
            if (configHealthStatus.lastRun.length === 0) {
                return "";
            }
            return format(parse(configHealthStatus.lastRun), "Do MMM YYYY HH:mm");
        }
    }
};
</script>


<style>
.config-health--is-late {
    color: red;
}
</style>
