import {DiscountService} from "./discount-service";

const discountService: DiscountService = new DiscountService();

describe("Discount", () => {
    test("validate", () => {
        const discount = discountService.factory();
        let result = discountService.validate(discount);

        result = discountService.validate(discount);
        expect(result.count.messages.length > 0).toBe(true);

        discount.count = 33;
        result = discountService.validate(discount);
        expect(result.discount).toBe(undefined);
    });
});
