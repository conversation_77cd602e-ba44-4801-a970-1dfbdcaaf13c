<template>
    <div>
        <!--mq: {{$mq}}-->
        <mq-layout :mq="['mobile', 'tablet']">
            <entry-mobile></entry-mobile>
        </mq-layout>
        <mq-layout mq="desktop">
            <entry></entry>
        </mq-layout>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import Component from "vue-class-component";
    import EntryMobile from "./entry-mobile.vue";
    import {ENTRY_STORE_CONST} from "./entry-store";
    import Entry from "./entry.vue";


    @Component({
        name: "entry-launch",
        components: {
            "entry": Entry,
            "entry-mobile": EntryMobile,
        }
    })
    export default class EntryLaunch extends Vue {

        public mounted() {
            this.$store.commit(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME + "/" +
                ENTRY_STORE_CONST.ENTRY_STORE_MUTATIONS_AUTO_SELECT,
                this.$route.query
            );
        }

    }
</script>
