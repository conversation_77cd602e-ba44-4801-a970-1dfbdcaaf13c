<template>
  <div>
    <div v-if="showSection === 'ENTRIES'">
      <!--      <div>-->
      <!--        hasBuilderPermissionForComp: {{compPermissions.hasBuilderPermissionForComp.value}}-->
      <!--      </div>-->

      <InfoSectionV2 info-type="error" v-if="hasBuilderPermissionForComp">
        <template slot>
          <div
            v-text="clubCompInfo.configData.clubName"
            class="e4s-header--400"
          ></div>
          <div v-html="userMessageEntriesHtml"></div>
          <div v-html="teamMessageEntriesHtml"></div>
        </template>
      </InfoSectionV2>

      <div
        class="
          e4s-flex-row e4s-justify-flex-row-vert-top
          e4s-gap--standard
          e4s-container--row-mobile-col
        "
      >
        <!--Date Picker-->
        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-flex-row e4s-justify-flex-row-vert-top
          "
        >
          <div v-if="scheduleDates.length === 1">
            <span v-text="getDate"></span>
          </div>

          <select
            class="browser-default e4s-input-field e4s-input-field--primary"
            v-model="scheduleDateDisplay"
            v-on:change="setSchedule"
          >
            <option :value="{ iso: '', display: 'ALL' }">ALL</option>

            <option
              v-for="startDate in scheduleDates"
              :key="startDate.iso"
              :value="startDate"
            >
              {{ startDate.display }}
            </option>
          </select>
        </div>
        <!--/Date Picker-->

        <ClubCompInfoClubPickerWrapper
          v-if="hasBuilderPermissionForComp"
          :comp-id="r4sCompSchedule.compId"
          :club-comp-info="clubCompInfo"
        />

        <InputDebounce
          placeholder="Filter..."
          :value="simpleFilterValue"
          v-on:input="applyFilters"
          :class="getFilterClass"
        />

        <div class="e4s-flex-row e4s-gap--standard">
          <InputCheckboxV2
            value-label="With Entries"
            :value="simpleFilterOnlyWithEntries"
            v-on:input="applyFilterOnlyWithEntries"
          />
        </div>
      </div>

      <table class="entity-entries--table">
        <tr class="e4s-header--500">
          <td class="entity-entries-event--time">Time</td>
          <td>Event Name</td>
          <td style="text-align: right">Count</td>
        </tr>

        <EntityEntriesEvent
          v-for="(scheduleTableRowState, index) in scheduleTableRowStates"
          :key="scheduleTableRowState.eventGroupId"
          :schedule-table-row-state="scheduleTableRowState"
          :club-comp-info="clubCompInfo"
          v-on:editBibs="editBibs"
        />
      </table>
    </div>

    <EntityEntriesEventEdit
      v-if="showSection === 'EDIT_BIBS'"
      :schedule-table-row-state="scheduleTableRowStateEdit"
      v-on:onCancel="showSection = 'ENTRIES'"
      v-on:onSubmit="onSubmitBibs"
    />

    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  Ref,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import {
  IR4sCompSchedule,
  IScheduleTableRow,
} from "../../../competition/scoreboard/rs4/rs4-scoreboard-models";
import * as EntityService from "./entity-service";
import {
  IClubCompInfo,
  IClubCompInfoEntryIndivAthlete,
} from "../schools/clubCompInfo-models";
import * as CompetitonServiceV2 from "../../../competition/v2/competiton-service-v2";
import EntityEntriesEvent from "./EntityEntriesEvent.vue";
import EntityEntriesEventEdit from "./EntityEntriesEventEdit.vue";
import {
  getCompResultDates,
  getIndivMessage,
  IScheduleTableRowState,
  userMessageTeams,
} from "../schools/clubCompInfoService";
import {
  ClubCompInfoData,
  ISubmitTeamBibNoPayload,
  ISubmitTeamBibNosPayload,
} from "../schools/clubCompInfoData";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { COMP_EVENT_TEAMS_STORE_CONST } from "../../../athleteCompSched/comp-event-teams/comp-event-store";
import { useStore } from "../../../app.store";
import ClubCompInfoClubPickerWrapper from "../schools/club-picker/ClubCompInfoClubPickerWrapper.vue";
import { useEntryStore } from "../../entry-store";
import { useCompPermissions } from "../../../config/useCompPermissions";
import {
  ICompetitionInfo,
  ICompetitionSummaryPublic,
} from "../../../competition/competition-models";
import InputDebounce from "../../../common/ui/field/input-debounce.vue";
import InputCheckboxV2 from "../../../common/ui/layoutV2/fields/input-checkbox-v2.vue";
import * as ClubCompInfoService from "../schools/clubCompInfoService";
import { ISimpleDateModel } from "../../../common/common-models";
import { format, parse } from "date-fns";
import { IScheduleFilterParams } from "./entity-service";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import InfoSectionV2 from "../../../common/ui/layoutV2/info-section-v2.vue";
import { ConfigService } from "../../../config/config-service";
import { useConfigStore } from "../../../config/useConfigStore";

export default defineComponent({
  name: "entity-entries",
  components: {
    InfoSectionV2,
    PrimaryLink,
    InputCheckboxV2,
    InputDebounce,
    ClubCompInfoClubPickerWrapper,
    LoadingSpinnerV2,
    EntityEntriesEventEdit,
    EntityEntriesEvent,
  },
  props: {
    r4sCompSchedule: {
      type: Object as PropType<IR4sCompSchedule>,
      required: true,
    },
    clubCompInfo: {
      type: Object as PropType<IClubCompInfo>,
      default: () => {
        return CompetitonServiceV2.factoryClubCompInfo();
      },
    },
  },
  setup(
    props: { r4sCompSchedule: IR4sCompSchedule; clubCompInfo: IClubCompInfo },
    context: SetupContext
  ) {
    const configService = new ConfigService();
    const configStore = useConfigStore();

    const scheduleTableRows: Ref<IScheduleTableRow[]> = ref<
      IScheduleTableRow[]
    >([]);

    const scheduleTableRowStates: Ref<IScheduleTableRowState[]> = ref<
      IScheduleTableRowState[]
    >([]);

    const showSection = ref<"ENTRIES" | "EDIT_BIBS">("ENTRIES");
    const scheduleTableRowStateEdit = ref<IScheduleTableRowState | null>(null);
    const isLoading = ref(false);
    const store = useStore();
    const entryStore = useEntryStore();

    const simpleFilterValue = ref("");
    const simpleFilterOnlyWithEntries = ref(false);

    const scheduleDateDisplay = ref({
      iso: "",
      display: "ALL",
    });

    const compPermissions = useCompPermissions(
      entryStore.entryForm
        .selectedCompetition as any as ICompetitionSummaryPublic
    );

    watch(
      () => [props.r4sCompSchedule, props.clubCompInfo],
      (newValue: unknown) => {
        // scheduleDateDisplay.value = getCompResultDates(
        //   props.r4sCompSchedule
        // )[0];

        setSchedule();
      },
      {
        immediate: true,
      }
    );

    watch(
      () => entryStore.entryForm.selectedCompetition,
      (newValue: ICompetitionInfo) => {
        compPermissions.initWithCompetitionInfo(newValue);
      },
      {
        immediate: true,
      }
    );

    function applyFilters(filterValue: string) {
      simpleFilterValue.value = filterValue;
      setSchedule();
    }

    function applyFilterOnlyWithEntries(onlyWithEntries: boolean) {
      simpleFilterOnlyWithEntries.value = onlyWithEntries;
      setSchedule();
    }

    function setSchedule() {
      let rows = EntityService.createScheduleTableRows(props.r4sCompSchedule);

      const filterParams: IScheduleFilterParams = {
        simpleFilterValue: simpleFilterValue.value,
        onlyWithEntries: simpleFilterOnlyWithEntries.value,
        scheduleDateDisplay: scheduleDateDisplay.value.iso,
      };

      // doing filtering further down...
      // if (simpleFilterValue.value.length > 0) {
      // rows = EntityService.filterScheduleTableRows(rows, filterParams);
      // }

      // not quite sure why  we need these
      // scheduleTableRows.value = rows;
      //
      let scheduleTableRowStatesTemp = rows.map((scheduleTableRow) => {
        return ClubCompInfoService.getScheduleTableRowState(
          scheduleTableRow,
          props.clubCompInfo
        );
      });

      //  TODO the filtering should be done here
      //  TODO:  Fix this filter
      // if (simpleFilterOnlyWithEntries.value) {
      //   scheduleTableRowStatesTemp = scheduleTableRowStatesTemp.filter(
      //     (scheduleTableRowState) => {
      //       return scheduleTableRowState.entries.length > 0;
      //     }
      //   );
      // }

      //filterScheduleTableRowStates
      scheduleTableRowStatesTemp = EntityService.filterScheduleTableRowStates(
        scheduleTableRowStatesTemp,
        filterParams
      );

      scheduleTableRowStates.value = scheduleTableRowStatesTemp;
    }

    function editBibs(scheduleTableRowState: IScheduleTableRowState) {
      scheduleTableRowStateEdit.value = scheduleTableRowState;
      showSection.value = "EDIT_BIBS";
    }

    function onSubmitBibs(
      data: Record<string, IClubCompInfoEntryIndivAthlete>
    ) {
      const submitTeamBibNoPayloads: ISubmitTeamBibNoPayload[] = Object.keys(
        data
      ).map((key) => {
        const athlete = data[key];
        const submitTeamBibNoPayload: ISubmitTeamBibNoPayload = {
          id: athlete.entryId,
          teamBibNo: athlete.teamBibNo,
        };
        return submitTeamBibNoPayload;
      });

      isLoading.value = true;
      const payload: ISubmitTeamBibNosPayload = {
        entries: submitTeamBibNoPayloads,
      };
      const prom = new ClubCompInfoData().submitTeamBibNos(payload);
      handleResponseMessages(prom);
      prom
        .then((resp) => {
          if (resp.errNo === 0) {
            //reload

            return store.dispatch(
              COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_CONST_MODULE_NAME +
                "/" +
                COMP_EVENT_TEAMS_STORE_CONST.COMP_EVENT_TEAMS_ACTIONS_GET_TEAM_HEADERS,
              {
                compId: props.r4sCompSchedule.compId,
              }
            );
          }
          return;
        })
        .finally(() => {
          isLoading.value = false;
          showSection.value = "ENTRIES";
        });
    }

    const getFilterClass = computed(() => {
      return simpleFilterValue.value.length > 0
        ? "entity-entries--filter-enabled"
        : "";
    });

    const userMessageEntriesHtml = computed(() => {
      return getIndivMessage(props.clubCompInfo);
    });

    // const userMessageEntriesHtml = computed( () => {
    //   // return ClubCompInfoService.userMessageEntriesHtml(props.clubCompInfo)
    //   return props.clubCompInfo.configData.clubName + ": " +
    //     getIndivMessage(props.clubCompInfo) +
    //     userMessageTeams(props.clubCompInfo);
    //   // ClubCompInfoService.userMessageTeamsHtml(clubCompInfo)
    // })

    const teamMessageEntriesHtml = computed(() => {
      // TODO qwerty  pass in eventTeamHeader props
      return userMessageTeams(props.clubCompInfo, []);
    });

    const scheduleDates = computed(() => {
      return getCompResultDates(props.r4sCompSchedule);
    });

    function isDateSelected(simpleDateModel: ISimpleDateModel): boolean {
      return simpleDateModel.iso === scheduleDateDisplay.value.iso;
    }

    const getDate = computed(() => {
      return format(parse(props.r4sCompSchedule.date), "Do MMM YYYY");
    });

    function selectedScheduleByDate(startDate: ISimpleDateModel) {
      scheduleDateDisplay.value = startDate;
      setSchedule();
    }

    const hasBuilderPermissionForComp = computed(() => {
      return configService.hasBuilderPermissionForComp(
        configStore.configApp.userInfo,
        props.r4sCompSchedule.org.id,
        props.r4sCompSchedule.compId
      );
      // return compPermissions.hasBuilderPermissionForComp.value;
    });

    return {
      simpleFilterValue,
      compPermissions,
      scheduleTableRows,
      scheduleTableRowStateEdit,
      showSection,
      isLoading,
      getFilterClass,
      simpleFilterOnlyWithEntries,
      scheduleTableRowStates,
      userMessageEntriesHtml,
      teamMessageEntriesHtml,
      scheduleDates,
      getDate,
      scheduleDateDisplay,

      hasBuilderPermissionForComp,

      setSchedule,
      applyFilters,
      selectedScheduleByDate,
      isDateSelected,
      applyFilterOnlyWithEntries,
      editBibs,
      onSubmitBibs,
    };
  },
});
</script>

<style>
.entity-entries--filter-enabled {
  border-color: var(--green-600);
  border-width: 2px;
  background-color: var(--green-50);
}
.entity-entries--comp-date-link-selected {
  color: black;
}

.entity-entries--table td {
  border-top: 1px solid lightgrey;
  padding: var(--e4s-gap--tiny) 0px;
}
.entity-entries-event--time {
  min-width: 50px;
  width: 100px;
}

@media screen and (max-width: 768px) {
  .entity-entries-event--time {
    min-width: 50px;
    width: 50px;
  }
}
</style>
