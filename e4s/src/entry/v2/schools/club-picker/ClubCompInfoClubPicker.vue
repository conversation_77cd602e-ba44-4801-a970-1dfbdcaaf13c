<template>
  <select
    v-model="selectedClub"
    class="browser-default e4s-input-field e4s-input-field--primary"
    v-on:change="onSelected"
  >
    <option v-for="club in clubs" :value="club" v-text="club.clubName"></option>
  </select>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  SetupContext,
  watch,
} from "@vue/composition-api";
import { IClubCompInfo, IClubCompInfoClub } from "../clubCompInfo-models";
import { factoryClubCompInfoComp } from "../../../../competition/v2/competiton-service-v2";
import { simpleClone } from "../../../../common/common-service-utils";

export default defineComponent({
  name: "club-comp-info-club-picker",
  components: {},
  props: {
    clubCompInfo: {
      type: Object as PropType<IClubCompInfo>,
      required: true,
    },
    setDefaultClub: {
      type: Boolean,
      default: true,
    },
  },
  setup(
    props: { clubCompInfo: IClubCompInfo; setDefaultClub: boolean },
    context: SetupContext
  ) {
    const pleaseSelectClub: IClubCompInfoClub = {
      ...factoryClubCompInfoComp(),
      clubName: "Please select",
    };

    const selectedClub = ref<IClubCompInfoClub>(pleaseSelectClub);
    const clubs = ref<IClubCompInfoClub[]>([]);

    watch(
      () => props.clubCompInfo,
      (newValue: IClubCompInfo) => {
        setOptions(newValue);
      },
      {
        immediate: true,
      }
    );

    function setOptions(clubCompInfo: IClubCompInfo) {
      clubs.value = [pleaseSelectClub, ...clubCompInfo.clubs];
      if (props.setDefaultClub) {
        setDefault(clubCompInfo);
      }
    }

    function setDefault(clubCompInfo: IClubCompInfo) {
      clubs.value.forEach((club) => {
        if (club.clubCompId === clubCompInfo.configData.clubCompId) {
          selectedClub.value = club;
        }
      });
    }

    function onSelected() {
      context.emit("onSelected", simpleClone(selectedClub.value));
    }

    // const getOptions = computed(() => {
    //   return [pleaseSelectClub, ...props.clubCompInfo.clubs];
    // });

    return { selectedClub, onSelected, clubs };
  },
});
</script>
