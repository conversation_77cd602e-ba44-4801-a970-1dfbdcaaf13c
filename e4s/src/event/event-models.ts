import {EVENT_TYPE, IEoptions, IUnique} from "../athletecompsched/athletecompsched-models";
import {GENDER, IBase} from "../common/common-models";
import {IEventUomType} from "../uom/uom-models";

export interface IEventSummary extends IBase {
    name: string;
    gender: GENDER;
    tf: EVENT_TYPE;
}

export interface IEventE4S extends IBase {
    name: string;                           //  Required
    tf: EVENT_TYPE;                         //  Required T  F  Track or Field
    gender: GENDER;                         //  Required
    options: IEoptions;
    uom: IEventUomType;                     //  Required
}

export interface IUniqueEventDisplay extends IUnique, IEventE4S {
}
