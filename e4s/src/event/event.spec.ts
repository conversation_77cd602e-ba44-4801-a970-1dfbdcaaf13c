import {EventService} from "./event-service";

import {EVENT_TYPE, IUnique} from "../athletecompsched/athletecompsched-models";
import {IEventE4S} from "./event-models";
import {IValidationResult} from "../common/common-models";
import {IEventUomType} from "../uom/uom-models";

describe("EventService", () => {
    const eventService: EventService = new EventService();

    test("convertEventTypeEnumToArray", () => {
        const eventTypes = EVENT_TYPE;
        const result = eventService.convertEventTypeEnumToArray(eventTypes)[0];
        expect(Object.keys(result).length).toBe(2);
    });

    test("validate", () => {
        const eventE4s: IEventE4S = {
            id: 0,
            name: "",
            gender: "",
            tf: "",
            options: {
                helpText: "",
                min: 0,
                max: 0,
                excludeFromCntRule: false,
                unique: [] as IUnique[],
                eventTeam: {
                    min: 0,
                    max: 0,
                    minTargetAgeGroupCount: 0,
                    maxOtherAgeGroupCount: 0,
                    teamPositionLabel: "",
                    maxEventTeams: 0,
                    currCount: 0,
                    singleClub: false,
                    showForm: false
                },
                rowOptions: {
                    autoExpandHelpText: false,
                    showPB: true,
                    showPrice: true,
                    showEntryCount: false
                }
            },
            uom: {} as IEventUomType
        } as IEventE4S;

        eventE4s.options.max = Number("");
        const result: IValidationResult[] = eventService.validate(eventE4s);
        // console.log("...", result);
        expect(result.length).toBe(3);
    });


    test("removeUnwantedDataForSubmission", () => {
        const eventE4s: IEventE4S = {
            id: 0,
            name: "Ping Pong",
            gender: "F",
            tf: "TRACK",
            options: {
                helpText: "blah blah",
                mn: 0,
                max: 0,
                excludeFromCntRule: false,
                unique: [
                    {
                    tf: "T",
                    options: {
                        min: 9.6,
                        max: 20,
                        class: "0,11-13,20,35-47,61-64"
                    },
                    id: 62,
                    name: "100m Open",
                    gender: "F",
                    uom: {
                        id: 1,
                        type: "T",
                        options: [{
                            pattern: "s.SS",
                            text: "seconds",
                            short: "s"
                        }]
                    },
                    e: 62,
                    ce: 0
                }, {
                    tf: "T",
                    options: {
                        min: "5.30.00",
                        max: "20.00.00",
                        unit: "mins"
                    },
                    id: 512,
                    name: "3000sc",
                    gender: "F",
                    uom: {
                        id: 11,
                        type: "T",
                        options: [{
                            pattern: "m.ss.SS",
                            text: "minutes",
                            short: "m"
                        }, {
                            pattern: "s.SS",
                            text: "seconds",
                            short: "s"
                        }]
                    },
                    e: 512,
                    ce: 0
                    }
                ],
                eventTeam: {
                    min: 0,
                    max: 0,
                    minTargetAgeGroupCount: 0,
                    maxOtherAgeGroupCount: 0,
                    teamPositionLabel: "",
                    maxEventTeams: 0,
                    currCount: 0,
                    singleClub: false,
                    showForm: false
                },
                rowOptions: {
                    autoExpandHelpText: false,
                    showPB: true,
                    showPrice: true,
                    showEntryCount: false
                }
            },
            uom: [{
                pattern: "mm.ss.SS",
                text: "mins",
                short: "m"
            }, {
                pattern: "ss.SS",
                text: "secs",
                short: "s"
            }]
        } as any as IEventE4S;
        let unique = eventE4s.options.unique;
        // console.log("before:", unique);
        const result = eventService.removeUnwantedDataForSubmission(eventE4s);
        unique = result.options.unique;
        // console.log("after:", unique);
        expect(unique.length).toBe(2);
    });

});
