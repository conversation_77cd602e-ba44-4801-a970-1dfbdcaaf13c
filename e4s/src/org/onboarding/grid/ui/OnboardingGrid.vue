<template>
  <div>
    <OrgGrid
      :value="onboardingGridController.state.orgs"
      :show-approve="true"
      v-if="onboardingGridController.state.ui.showSection === 'grid'"
      @onApprove="onboardingGridController.onApproveGetConfirmation"
    />

    <div
      class="e4s-flex-column"
      v-if="onboardingGridController.state.ui.showSection === 'approve'"
    >
      <div class="e4s-flex-row e4s-gap--standard">
        <span>Are you sure you want to approve this organization:</span>
        <span v-text="onboardingGridController.displayName.value"></span>
      </div>

      <div class="e4s-flex-row e4s-gap--standard e4s-flex-row--end">
        <ButtonGenericV2
          v-on:click="onboardingGridController.onCancelApproveOrg"
          text="Cancel"
          button-type="tertiary"
          class="e4s-button--150"
        />
        <ButtonGenericV2
          text="Confirm Approve"
          @click="onboardingGridController.onApproveOrg"
          class="e4s-button--150"
        />
      </div>
    </div>

    <LoadingSpinnerV2 v-if="onboardingGridController.state.isLoading" />
  </div>
</template>

<script lang="ts">
import { defineComponent, SetupContext } from "@vue/composition-api";
import { useOnboardingGridController } from "../model/useOnboardingGridController";
import OrgGrid from "../../ui/OrgGrid.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";

export default defineComponent({
  name: "OnboardingGrid",
  components: { ButtonGenericV2, LoadingSpinnerV2, OrgGrid },
  props: {},
  setup(props: any, context: SetupContext) {
    const onboardingGridController = useOnboardingGridController();

    onboardingGridController.init();

    return { onboardingGridController };
  },
});
</script>
