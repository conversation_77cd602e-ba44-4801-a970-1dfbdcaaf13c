<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <label class="active" :for="PREFIX + 'name'">
          Organisation Name (id: <span v-text="org.id"></span>)
        </label>
        <input :id="PREFIX + 'name'" v-model="org.name" />
      </div>
    </div>

    <div class="row">
      <div class="input-field col s12 m12 l12">
        <file-upload
          v-on:onUpload="onLogoUpload"
          class="e4s-force-inline-block"
        >
        </file-upload>
        <div class="e4s-force-inline-block">
          <a :href="getLogoLink" target="_blank">
            <span>Open</span>
          </a>
        </div>
        <label class="active" for="logo"> Logo </label>
        <input
          id="logo"
          name="logo"
          v-model="org.logo"
          :placeholder="'Link to Logo'"
        />
      </div>
    </div>

    <div class="row">
      <div class="col s12 m9 l10">
        <label class="active" :for="PREFIX + 'name'"> Stripe User </label>
        <div
          v-if="getHasStripe"
          v-text="this.org.stripeUser.name"
          v-show="!showEditStripeUser"
        ></div>
        <UserSearchTypeAhead
          v-on:onSelected="onSearchFound"
          v-show="showEditStripeUser"
        ></UserSearchTypeAhead>
      </div>
      <div class="col s12 m3 l2">
        <div class="right">
          <ButtonGenericV2
            :button-type="showEditStripeUser ? 'tertiary' : 'primary'"
            @click="showEditStripeUser = !showEditStripeUser"
            :text="showEditStripeUser ? 'Cancel' : 'Edit'"
          />
          <!--                    <button class="btn waves-effect waves green" v-on:click.stop="showEditStripeUser = !showEditStripeUser">-->
          <!--                        <span v-text="showEditStripeUser ? 'Cancel' : 'Edit'"></span>-->
          <!--                    </button>-->
        </div>
      </div>
    </div>

    <div class="e4s-section-padding-separator"></div>

    <TandcUsers></TandcUsers>

    <div class="e4s-section-padding-separator"></div>

    <div class="row">
      <div class="col s12 m12 l12">
        <div class="right">
          <ButtonGenericV2
            text="Cancel"
            button-type="tertiary"
            :disabled="isLoading"
            @click="onCancel()"
          />
          <!--          <button-->
          <!--            :disabled="isLoading"-->
          <!--            class="btn waves-effect waves grey"-->
          <!--            v-on:click.stop="onCancel()"-->
          <!--          >-->
          <!--            Cancel-->
          <!--          </button>-->

          <ButtonGenericV2
            text="Save"
            :disabled="isLoading"
            @click="onSubmit()"
          />
          <!--          <button-->
          <!--            :disabled="isLoading"-->
          <!--            class="btn waves-effect waves green"-->
          <!--            v-on:click.stop="onSubmit()"-->
          <!--          >-->
          <!--            Save-->
          <!--          </button>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { IOrg } from "./org-models";
import { OrgService } from "./org-service";
import FileUpload from "../common/ui/file-upload.vue";
import UserSearchTypeAhead from "../admin/user/user-search-type-ahead.vue";
import { IUserSummary } from "../admin/user/user-models";
import TandcUsers from "./tandc/tandc-users.vue";
import ButtonGenericV2 from "../common/ui/layoutV2/buttons/button-generic-v2.vue";

const orgService: OrgService = new OrgService();

@Component({
  name: "org-form",
  components: {
    ButtonGenericV2,
    TandcUsers,
    UserSearchTypeAhead,
    "file-upload": FileUpload,
  },
})
export default class OrgForm extends Vue {
  @Prop({
    default: () => {
      return orgService.factory();
    },
  })
  public readonly orgProp: IOrg;
  @Prop({
    default: false,
  })
  public readonly isLoading: boolean;

  public org: IOrg = orgService.factory();
  public PREFIX = Math.random().toString(36).substring(2);
  public showEditStripeUser = false;

  public created() {
    // this.org = R.clone(this.orgProp);
    this.init(this.orgProp);
  }

  @Watch("orgProp")
  public onOrgPropChanged(newValue: IOrg) {
    // this.org = R.clone(newValue);
    this.init(newValue);
  }

  public init(org: IOrg) {
    const orgNew = R.clone(this.orgProp);
    if (!orgNew.options) {
      orgNew.options = orgService.factoryOrgOptions();
    }
    this.org = orgNew;
  }

  public onLogoUpload(logoPath: string) {
    this.org.logo = logoPath;
    if (this.org.id > 0) {
      this.onSubmit();
    }
  }

  public onSearchFound(userSummary: IUserSummary): void {
    if (R.isNil(userSummary) || userSummary.id === 0) {
      return;
    }
    this.org.stripeUser = {
      id: userSummary.id,
      name: userSummary.niceName,
      showPaymentCode: false,
    };
    this.showEditStripeUser = false;
  }

  public get getLogoLink() {
    return this.org.logo;
  }

  public get getHasStripe(): boolean {
    return this.org.stripeUser && this.org.stripeUser.id > 0;
  }

  public onSubmit() {
    this.$emit("onSubmit", R.clone(this.org));
  }

  public onCancel() {
    this.$emit("onCancel");
  }
}
</script>
