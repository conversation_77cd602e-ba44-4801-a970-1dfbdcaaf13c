import { IOrg } from "../../org-models";
import { factoryOrgGridControllerState } from "./org-search-gris-service";
import { reactive } from "@vue/composition-api";
import { OrgData } from "../../org-data";

export interface IOrgGridControllerInput {
  useGlobalState: boolean;
}

export interface IOrgGridControllerState {
  orgs: IOrg[];
  filterValues: {
    orgName: string;
  };
  isLoading: boolean;
}

export const globalState = factoryOrgGridControllerState();

export function useOrgGridController(
  orgGridControllerInput: IOrgGridControllerInput
) {
  const state = reactive(
    orgGridControllerInput.useGlobalState
      ? globalState
      : factoryOrgGridControllerState()
  );

  const orgData = new OrgData();

  function doSearch() {
    state.isLoading = true;

    orgData
      .list({
        startswith: state.filterValues.orgName,
        pagenumber: 1,
        pagesize: 50,
        sortkey: "name",
      })
      .then((response) => {
        state.orgs = response.data;
      })
      .finally(() => {
        state.isLoading = false;
      });
  }

  return {
    state,
    doSearch,
  };
}
