<template>
  <div class="e4s-flex-row" style="min-height: 50px">
    <div class="e4s-card e4s-card--generic e4s-full-width" :class="getCardCss">
      <div class="e4s-flex-column e4s-gap--standard">
        <h1 class="e4s-header--400">
          <div class="e4s-flex-row">
            <!--            competitionSummaryPublicController.getFirstCompDate.value +-->
            <!--            ' - ' +-->
            <div v-text="competitionSummaryPublic.compName"></div>

            <div
              v-text="'#' + competitionSummaryPublic.compId"
              class="e4s-flex-row--end"
            ></div>
          </div>
        </h1>

        <!--        v-if="competitionSummaryPublicController.isMultiDay.value"-->
        <div class="e4s-flex-row e4s-gap--standard">
          <span v-if="competitionSummaryPublic.options.dates.length > 1"
            >Multi day:</span
          >
          <span
            v-text="
              competitionSummaryPublicController.getCompDatesListForDisplay
                .value
            "
          ></span>
        </div>

        <hr class="dat-e4s-hr-only" />

        <div class="e4s-flex-row e4s-full-width e4s-justify-flex-space-between">
          <div v-text="competitionSummaryPublic.club"></div>

          <div class="e4s-flex-column e4s-flex-end">
            <div
              v-text="getPublicCardOpenCloseMessage"
              :class="
                competitionSummaryPublicController.isNearToClosing.value
                  ? 'e4s-info-text--error e4s-header--400'
                  : ''
              "
            ></div>
          </div>
        </div>

        <div class="e4s-flex-row e4s-full-width e4s-justify-flex-space-between">
          <div class="e4s-card--competition__logo-container">
            <img
              v-if="competitionSummaryPublicController.getLogo.value.length > 0"
              class="e4s-card--competition__logo-image"
              :src="competitionSummaryPublicController.getLogo.value"
            />
            <E4sLogoSvg
              style="height: 68px; padding: 5px"
              v-if="
                competitionSummaryPublicController.getLogo.value.length === 0
              "
            />
          </div>

          <a
            class="e4s-hyperlink--100 e4s-hyperlink--primary"
            href="#"
            v-on:click.prevent="goToResults"
          >
            <div
              class="
                e4s-flex-row e4s-flex-nowrap
                e4s-card-competition__participant-overview-container
              "
            >
              <CompetitionEntryCounts
                :competition-summary-public="competitionSummaryPublic"
                @click.prevent="goToResults"
              />
            </div>
          </a>
        </div>

        <div
          class="e4s-flex-column"
          v-if="competitionSummaryPublic.newsFlash.length > 0"
        >
          <div class="e4s-info-text--error">
            <p class="e4s-flex-column e4s-gap--standard-x">
              <span v-html="getNewsFlash"></span>
            </p>
          </div>
        </div>

        <div v-text="competitionSummaryPublic.options.homeInfo"></div>

        <div
          class="e4s-flex-column"
          v-if="competitionSummaryPublic.options.disabled"
        >
          <div
            class="e4s-info-text--error"
            v-html="competitionSummaryPublic.options.disabledReason"
          ></div>
        </div>

        <CompRestrictedV2
          v-if="competitionSummaryPublicController.hasAthleteSecurity.value"
          :athlete-security="competitionSummaryPublic.options.athleteSecurity"
          :show-athlete-profiles-link="false"
        />

        <div class="e4s-flex-row">
          <!--                    <ButtonGenericV2-->
          <!--                      text="Results"-->
          <!--                      v-if="-->
          <!--                        !isDisplayingMoreInfo &&-->
          <!--                        competitionSummaryPublic.options.resultsAvailable &&-->
          <!--                        showResultsButton-->
          <!--                      "-->
          <!--                      v-on:click="goToResults"-->
          <!--                    />-->
          <ButtonGotoGenericV2
            v-if="
              !isDisplayingMoreInfo &&
              competitionSummaryPublic.options.resultsAvailable &&
              showResultsButton
            "
            goto-type="RESULTS"
            :comp-id="competitionSummaryPublic.compId"
          />

          <ButtonGenericV2
            text="Check In"
            v-on:click="goToCheckIn"
            v-if="competitionSummaryPublic.options.checkIn.enabled"
          />

          <slot name="button-more-info">
            <ButtonGenericV2
              :button-type="
                competitionSummaryPublic.active ? 'primary' : 'tertiary'
              "
              :text="
                showMoreInfo
                  ? competitionSummaryPublicController.getEnterButtonText.value
                  : competitionSummaryPublicController
                      .getEnterButtonTextSimpleCard.value
              "
              class="e4s-button--auto e4s-flex-row--end"
              v-on:click="doProcessNext"
              v-if="!isDisplayingMoreInfo"
            />
          </slot>
        </div>

        <template
          v-if="
            isAdmin && competitionSummaryPublicController.isPastCompDate.value
          "
        >
          <hr class="dat-e4s-hr-only" />
          <div
            class="e4s-flex-row e4s-justify-flex-space-between"
            v-if="competitionSummaryPublicController.isPastCompDate.value"
          >
            <div class="e4s-flex-row e4s-gap--standard">
              Status
              <span
                v-text="
                  competitionSummaryPublicController.getStatusDescription.value
                "
              ></span>
            </div>

            <ButtonGenericV2
              text="Edit"
              button-type="secondary"
              class="e4s-button--slim"
              style="width: 50px"
              @click="
                competitionSummaryPublicController.isEditingStatus.value = true
              "
            />

            <ModalV2
              :is-full-screen="$mq === VUE_MQ_SIZES.MOBILE.name"
              v-if="competitionSummaryPublicController.isEditingStatus.value"
              :always-show-header-blank="true"
            >
              <StatusContainer
                slot="body"
                :competition-summary-public-prop="competitionSummaryPublic"
                :work-flows="compStoreState.workFlows"
                v-on:onClose="
                  competitionSummaryPublicController.isEditingStatus.value = false
                "
              >
              </StatusContainer>
            </ModalV2>
          </div>
        </template>
      </div>
      <slot name="card-footer"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  PropType,
  SetupContext,
} from "@vue/composition-api";
import { ICompetitionSummaryPublic } from "../../../../competition/competition-models";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import { useRouter } from "../../../../router/migrateRouterVue3";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import CompHeaderLogoCountsV2 from "./comp-header-logo-counts-v2.vue";
import CompHeaderNameLocationV2 from "./comp-header-name-location-v2.vue";
import ButtonGotoCompV2 from "../../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import ButtonGotoResultsV2 from "../../../../common/ui/layoutV2/buttons/button-goto-results-v2.vue";
import { CompetitionService } from "../../../../competition/competiton-service";
import { getE4sStandardHumanDateTimeOutPut } from "../../../../common/common-service-utils";
import { compareDesc, isAfter, parse } from "date-fns";
import { CompMoreInfoSection } from "./moreinfo/comp-more-info-models";
import InputWithButton from "../../../../common/ui/layoutV2/fields/InputWithButton.vue";
import FieldSelectV2 from "../../../../common/ui/layoutV2/fields/field-select-v2.vue";
import E4sLogoSvg from "../../../../common/ui/svg/E4sLogoSvg.vue";
import * as CompetitonServiceV2 from "../../../../competition/v2/competiton-service-v2";
import { useAuthStore } from "../../../../auth/useAuthStore";
import { LAUNCH_ROUTES_PATHS_V2 } from "../../../../launch/v2/launch-routes-v2";
import { useCompetitionSummaryPublicController } from "../../../../competition/v2/useCompetitionSummaryPublicController";
import CounterV2 from "../../../../common/ui/layoutV2/counter/counter-v2.vue";
import CompetitionEntryCounts from "../../../../competition/v2/CompetitionEntryCounts.vue";
import CompRestrictedV2 from "../../../../competition/restricted/CompRestrictedV2.vue";
import ModalV2 from "../../../../common/ui/layoutV2/modal/modal-v2.vue";
import StatusContainer from "../../../../competition/status/status-container.vue";
import { useCompStoreState } from "../../public-comps-store";
import { VUE_MQ_SIZES } from "../../../../index";
import ButtonGotoGenericV2 from "../../../../common/ui/layoutV2/buttons/button-goto-generic-v2.vue";

export default defineComponent({
  name: "PublicCompCardV2",
  methods: { compareDesc },
  computed: {
    VUE_MQ_SIZES() {
      return VUE_MQ_SIZES;
    },
  },
  components: {
    ButtonGotoGenericV2,
    StatusContainer,
    ModalV2,
    CompRestrictedV2,
    CompetitionEntryCounts,
    CounterV2,
    E4sLogoSvg,
    FieldSelectV2,
    InputWithButton,
    ButtonGotoResultsV2,
    ButtonGotoCompV2,
    CompHeaderNameLocationV2,
    CompHeaderLogoCountsV2,
    ButtonGenericV2,
  },
  props: {
    competitionSummaryPublic: {
      type: Object as PropType<ICompetitionSummaryPublic>,
      required: true,
    },
    showGoToComp: {
      type: Boolean,
      default: true,
    },
    showMoreInfo: {
      type: Boolean,
      default: true,
    },
    isDisplayingMoreInfo: {
      type: Boolean,
      default: false,
    },
    showResultsButton: {
      type: Boolean,
      default: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
  },
  setup(
    props: {
      competitionSummaryPublic: ICompetitionSummaryPublic;
      showGoToComp: boolean;
      showMoreInfo: boolean;
      isDisplayingMoreInfo: boolean;
      showResultsButton: boolean;
      isAdmin: boolean;
    },
    context: SetupContext
  ) {
    const router = useRouter();
    const competitionService = new CompetitionService();
    let authStore = useAuthStore();
    const competitionSummaryPublicController =
      useCompetitionSummaryPublicController(props.competitionSummaryPublic);

    const compStoreState = useCompStoreState();

    function contactOrganiser() {
      context.emit("showContactOrganiser", props.competitionSummaryPublic);
    }

    function goToResults() {
      doShowMoreInfo("SCHEDULE");
    }

    function goToCheckIn() {
      //  goto route: checkin-athlete/:compId
      const checkinUrl =
        "/" +
        LAUNCH_ROUTES_PATHS.ATHLETE_CHECKIN +
        "/" +
        props.competitionSummaryPublic.compId;
      router.push(checkinUrl);
    }

    function doShowMoreInfo(compMoreInfoSection: CompMoreInfoSection) {
      context.emit("showMoreInfo", {
        competitionSummaryPublic: props.competitionSummaryPublic,
        compMoreInfoSection: compMoreInfoSection,
      });
    }

    const getCheckinDateTime = computed(() => {
      const checkInDateTimeOpens =
        props.competitionSummaryPublic.options.checkIn.checkInDateTimeOpens;
      if (checkInDateTimeOpens.length === 0) {
        return "";
      }
      return getE4sStandardHumanDateTimeOutPut(checkInDateTimeOpens, false);
    });

    const getEntryCloseDate = computed(() => {
      return getE4sStandardHumanDateTimeOutPut(
        props.competitionSummaryPublic.closedate,
        false
      );
    });

    const getEntryOpenDate = computed(() => {
      return getE4sStandardHumanDateTimeOutPut(
        props.competitionSummaryPublic.opendate,
        false
      );
    });

    const isPastOpenDate = computed(() => {
      return isAfter(
        new Date(),
        parse(props.competitionSummaryPublic.opendate)
      );
    });

    const getLateEntryDate = computed(() => {
      if (competitionService.hasSaleEndDate(props.competitionSummaryPublic)) {
        return competitionService.getSaleEndMessage(
          props.competitionSummaryPublic
        );
      }
      return "";
    });

    const getPublicCardOpenCloseMessage = computed(() => {
      return CompetitonServiceV2.getPublicCardOpenCloseMessage(
        props.competitionSummaryPublic
      );
    });

    // The input field restricts to 300 chars, but upping this to 500 for any legacy data.
    const MAX_NEWSFLASH_LENGTH = 500;

    const getNewsFlash = computed(() => {
      //  remove paragraph tags
      const newsFlash = props.competitionSummaryPublic.newsFlash.replace(
        /<\/?p[^>]*>/g,
        ""
      );

      if (newsFlash.length < MAX_NEWSFLASH_LENGTH) {
        return newsFlash;
      }
      return newsFlash.slice(0, MAX_NEWSFLASH_LENGTH) + "...";
    });

    const showMoreNewsFlash = computed(() => {
      return (
        props.competitionSummaryPublic.newsFlash.length > MAX_NEWSFLASH_LENGTH
      );
    });

    function goToComp() {
      console.log("PublicCompCardV2.goToComp");
      const isV2Route = router.currentRoute.path.startsWith("/v2/");
      console.log("PublicCompCardV2.isV2Route: " + isV2Route);

      const useV2Route =
        props.competitionSummaryPublic.options.useV2Routes || isV2Route;
      console.log("PublicCompCardV2.useV2Route: " + useV2Route);

      if (
        props.competitionSummaryPublic.options.ui.entryDefaultPanel ===
        "SHOP_ONLY"
      ) {
        const shopUrlV1 =
          "/" +
          LAUNCH_ROUTES_PATHS.SHOP +
          "/" +
          props.competitionSummaryPublic.compId;
        console.log("PublicCompCardV2.goToComp: " + shopUrlV1);

        // const shopUrlV2 =
        //   "/v2/" +
        //   LAUNCH_ROUTES_PATHS_V2.SHOP_V2 +
        //   "/" +
        //   props.competitionSummaryPublic.compId;
        // console.log("PublicCompCardV2.goToComp: " + shopUrlV2);

        // const shopUrl = useV2Route ? shopUrlV2 : shopUrlV1;
        const shopUrl = shopUrlV1;
        console.log("PublicCompCardV2.goToComp: " + shopUrl);

        router.push({
          path: shopUrl,
        });
        return;
      }

      const entryUrlV1 =
        "/" +
        LAUNCH_ROUTES_PATHS.ENTRY +
        "?comporgid=" +
        props.competitionSummaryPublic.compOrgId +
        "&compid=" +
        props.competitionSummaryPublic.compId;
      console.log("PublicCompCardV2.goToComp: " + entryUrlV1);

      const entryUrlV2 =
        "/v2/" +
        LAUNCH_ROUTES_PATHS_V2.ENTRY_V2 +
        "/" +
        props.competitionSummaryPublic.compId;
      console.log("PublicCompCardV2.goToComp: " + entryUrlV2);

      const compUrl = useV2Route ? entryUrlV2 : entryUrlV1;
      console.log("PublicCompCardV2.goToComp: " + compUrl);

      const loginUrlV1 = "/login";
      const loginUrlV2 = "/v2/" + LAUNCH_ROUTES_PATHS_V2.LOGIN_V2;

      const loginUrl = useV2Route ? loginUrlV2 : loginUrlV1;
      console.log("PublicCompCardV2.goToComp: " + loginUrl);

      let location: RawLocation;

      let isLoggedIn = authStore.isLoggedIn;

      if (!isLoggedIn) {
        location = {
          path: loginUrl,
          query: {
            redirectFrom: compUrl,
          },
        };
      } else {
        location = {
          path: compUrl,
        };
      }

      router.push(location);
    }

    function doProcessNext() {
      if (props.showMoreInfo) {
        goToComp();
        return;
      }
      doShowMoreInfo("INFO");
    }

    const isCompActive = computed(() => {
      return CompetitonServiceV2.isCompActive(props.competitionSummaryPublic);
    });

    const getCardCss = computed<string[]>(() => {
      const css: string[] = [];

      if (props.isDisplayingMoreInfo) {
        css.push("e4s-card--transparent");
      }
      // else {
      //   css.push("e4s-square--right");
      // }

      if (!isCompActive.value) {
        css.push("public-list-comp-card-v2--inactive");
      }
      return css;
    });

    return {
      competitionSummaryPublicController,
      contactOrganiser,
      goToResults,
      doShowMoreInfo,
      isPastOpenDate,
      getEntryOpenDate,
      getCheckinDateTime,
      getEntryCloseDate,
      getLateEntryDate,
      goToComp,
      goToCheckIn,
      getPublicCardOpenCloseMessage,
      doProcessNext,
      getNewsFlash,
      MAX_NEWSFLASH_LENGTH,
      showMoreNewsFlash,
      getCardCss,
      isCompActive,
      compStoreState,
    };
  },
});
</script>

<style scoped>
.public-list-comp-card-v2--inactive {
  color: var(--slate-400);
}
</style>
