<template>
  <div
    class="e4s-content-wrapper e4s-content-max-center"
    style="padding: var(--e4s-gap--standard)"
  >
    <LoadingSpinnerV2 v-if="isLoading" />
    <div class="e4s-flex-row e4s-justify-flex-center">
      <div class="e4s-flex-column e4s-gap--standard e4s-width-controller">
        <CompMoreInfoV2
          :competition-summary-public="competitionSummaryPublic"
          v-if="
            competitionSummaryPublic.compId > 0 && showSection === 'MORE_INFO'
          "
          :show-comp-results-buttons="true"
          :show-bottom-button-bar="false"
          v-on:showContactOrganiser="setShowSection('CONTACT_ORGANISER')"
          @goToCompV1="goToComp"
          @onOrgApproved="onOrgApproved"
        />

        <div
          v-if="showSection === 'CONTACT_ORGANISER'"
          class="e4s-flex-column e4s-gap--standard"
        >
          <PublicCompCardV2
            :competition-summary-public="competitionSummaryPublic"
          >
            <ButtonGotoCompV2
              slot="button-more-info"
              class="e4s-flex-row--end"
              :competition-summary-public="competitionSummaryPublic"
              :config-version="configController.getVersion.value"
              @goToCompV1="goToComp"
            />
          </PublicCompCardV2>

          <div class="e4s-card e4s-card--generic">
            <AskOrganiserFormV2
              :competition-summary-public="competitionSummaryPublic"
              v-on:cancel="setShowSection('MORE_INFO')"
            >
              <PrimaryLink
                slot="top-right-back-button"
                link-text="Back"
                @onClick="setShowSection('MORE_INFO')"
              />
            </AskOrganiserFormV2>
          </div>
        </div>

        <div v-if="showSection === 'PRIORITY'">
          <PriorityV2
            style="padding: var(--e4s-gap--standard) 0"
            :competition-summary-public="competitionSummaryPublic"
            v-on:cancel="priorityCodeResult(false)"
            v-on:submit="priorityCodeResult(true)"
          />
        </div>

        <div v-if="showSection === 'TERMS_CONDITIONS'">
          <TermsConditionsV2
            style="padding: var(--e4s-gap--standard) 0"
            :competition-summary-public="competitionSummaryPublic"
            v-on:cancel="priorityCodeResult(false)"
            v-on:submit="handleTermsConditionsResult(true)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, SetupContext } from "@vue/composition-api";
import { CompetitionService } from "../../../competition/competiton-service";
import { CompetitionData } from "../../../competition/competition-data";
import { handleResponseMessages } from "../../../common/handle-http-reponse";
import { useRoute, useRouter } from "../../../router/migrateRouterVue3";
import CompMoreInfoV2 from "../public-list/v2/moreinfo/comp-more-info-v2.vue";
import LoadingSpinnerV2 from "../../../common/ui/loading-spinner-v2.vue";
import AskOrganiserFormV2 from "../../../competition/askorganiser/ask-organiser-form-v2.vue";
import PublicCompCardV2 from "../public-list/v2/public-list-comp-card-v2.vue";
import ButtonGotoCompV2 from "../../../common/ui/layoutV2/buttons/button-goto-comp-v2.vue";
import PrimaryLink from "../../../common/ui/layoutV2/href/PrimaryLink.vue";
import { useConfigController } from "../../../config/useConfigStore";
import { ICompetitionSummaryPublic } from "../../../competition/competition-models";
import { simpleClone } from "../../../common/common-service-utils";
import { isBefore, parse } from "date-fns";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../launch/launch-routes";
import { useAuthStoreController } from "../../../auth/useAuthStore";
import PriorityV2 from "../priority/priority-v2.vue";
import TermsConditionsV2 from "../terms-conditions/terms-conditions-v2.vue";

const competitionService = new CompetitionService();

type EntryDirectV1SectionType =
  | "CONTACT_ORGANISER"
  | "MORE_INFO"
  | "TERMS_CONDITIONS";

export default defineComponent({
  name: "EntryDirectV1",
  components: {
    TermsConditionsV2,
    PriorityV2,
    PrimaryLink,
    ButtonGotoCompV2,
    PublicCompCardV2,
    AskOrganiserFormV2,
    LoadingSpinnerV2,
    CompMoreInfoV2,
  },
  setup(props: {}, context: SetupContext) {
    const isLoading = ref(false);
    const route = useRoute();
    const competitionSummaryPublic = ref(
      competitionService.factorySummaryPublic()
    );

    const configController = useConfigController();
    const authStoreController = useAuthStoreController();

    const showSection = ref<EntryDirectV1SectionType>("MORE_INFO");
    const routerInternal = useRouter();

    const compId = isNaN(Number(route.params.id))
      ? 0
      : parseInt(route.params.id, 0);

    loadComp();

    function loadComp() {
      if (compId > 0) {
        isLoading.value = true;
        const prom = new CompetitionData().getCompById(compId);
        handleResponseMessages(prom);
        prom
          .then((resp) => {
            if (resp.errNo === 0) {
              const comp = resp.data;
              competitionSummaryPublic.value = comp;
            }
          })
          .finally(() => {
            isLoading.value = false;
          });
      }
    }

    function goToComp() {
      console.log("EntryPublicV2V1Wrapper.goToCompV1");
      const comp = simpleClone(competitionSummaryPublic.value);

      const priorityDate =
        comp.options.priority &&
        comp.options.priority.dateTime &&
        comp.options.priority.dateTime.length > 0
          ? comp.options.priority.dateTime
          : "";

      const priorityAlwaysRequired =
        priorityDate.length === 0 && comp.options.priority.required;

      if (comp.options.priority.required) {
        if (
          isBefore(new Date(), parse(comp.options.priority.dateTime)) ||
          priorityAlwaysRequired
        ) {
          let location: RawLocation;
          if (!authStoreController.isLoggedIn.value) {
            location = {
              path: "/login",
              query: {
                redirectFrom:
                  "/" +
                  LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS +
                  "/" +
                  comp.compId,
              },
            };
          } else {
            location = {
              path:
                "/" + LAUNCH_ROUTES_PATHS.ENTRY_CONDITIONS + "/" + comp.compId,
            };
          }
          routerInternal.push(location);
          return;
        }
      }

      if (comp.termsConditions.length > 0) {
        showSection.value = "TERMS_CONDITIONS";
        return;
      }

      proceedToComp(comp);
    }

    function priorityCodeResult(isOk: boolean) {
      if (!isOk) {
        showSection.value = "MORE_INFO";
        return;
      }

      const comp = competitionSummaryPublic.value;
      if (comp.termsConditions && comp.termsConditions.length > 0) {
        showSection.value = "TERMS_CONDITIONS";
        return;
      }
      proceedToComp(comp);
    }

    function handleTermsConditionsResult(isOk: boolean) {
      if (!isOk) {
        showSection.value = "MORE_INFO";
        return;
      }
      proceedToComp(competitionSummaryPublic.value);
    }

    function proceedToComp(comp: ICompetitionSummaryPublic) {
      if (comp.options.ui.entryDefaultPanel === "SHOP_ONLY") {
        routerInternal.push({
          path: "/shop/" + comp.compId,
        });
        return;
      }

      let location: RawLocation;
      if (!authStoreController.isLoggedIn.value) {
        location = {
          path: "/login",
          query: {
            redirectFrom:
              "/entry?comporgid=" + comp.compOrgId + "&compid=" + comp.compId,
          },
        };
      } else {
        location = {
          path: "/entry",
          query: {
            comporgid: comp.compOrgId.toString(),
            compid: comp.compId.toString(),
          },
        };
      }

      routerInternal.push(location);
    }

    function setShowSection(entryDirectSectionType: EntryDirectV1SectionType) {
      showSection.value = entryDirectSectionType;
    }

    function onOrgApproved() {
      console.log("EntryDirectV1.onOrgApproved");
      loadComp();
    }

    return {
      configController,
      isLoading,
      showSection,
      competitionSummaryPublic,
      setShowSection,
      goToComp,
      priorityCodeResult,
      handleTermsConditionsResult,
      onOrgApproved,
    };
  },
});
</script>

<style></style>
