<template>
    <div>
        <div class="row">

            <div class="input-field col s1 m1 l1">
                <span v-text="secondaryDef.id"></span>
            </div>

            <div class="input-field col s1 m1 l1">
                <span v-text="secondaryDef.refObj.objType"></span>
            </div>

            <div class="input-field col s5 m5 l5">
                <span v-text="getDescription"></span>
            </div>

            <div class="input-field col s1 m1 l1">
                <span v-text="getMaxAllowed"></span>
            </div>

            <div class="input-field col s1 m1 l1">
                <span v-text="secondaryDef.prod.price.price"></span>
            </div>

            <div class="input-field col s1 m1 l1">
                <span v-text="getQty"></span>
            </div>

            <div class="input-field col s4 m2 l2">
                <div class="right">
                    <slot name="delete-button">
                        <button class="btn btn-flat green-text e4s-bold"
                                v-show="!isLoading"
                                v-on:click.stop="onDeleted">
                            <span>Del</span>
                        </button>
                    </slot>
                    <slot name="cancel-button">
                        <button class="btn btn-flat green-text e4s-bold"
                                v-show="!isLoading"
                                v-on:click.stop="onEdited">
                            <span>Edit</span>
                        </button>
                    </slot>
                    <slot name="select-button">
                        <button class="btn btn-flat green-text e4s-bold"
                                v-show="!isLoading"
                                v-on:click.stop="onSelected">
                            <span>Select</span>
                        </button>
                    </slot>
                </div>
            </div>
        </div>

        <div class="row" v-if="getHasAttributes">
            <div class="input-field col s1 m1 l1">

            </div>
            <div class="input-field col s10 m10 l10">
                <span v-text="getAttributesDisplay"></span>
                <a v-if="getHasVariations" href="#" v-on:click.prevent="variationsExpanded = !variationsExpanded">
                    <span v-text="variationsExpanded ? 'Hide' : 'Show'"></span> variations
                </a>
            </div>
        </div>

        <div class="row" v-if="getHasVariations" v-show="variationsExpanded">
            <div v-for="(variation, index) in secondaryDef.prod.variations" class="input-field col s12 m12 l12">
                <SecondaryVariantRow :secondary-variant="variation">
                    <div slot="delete-button"></div>
                </SecondaryVariantRow>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import {Prop} from "vue-property-decorator"
import {ISecondaryDef} from "../secondary-models"
import * as R from "ramda"
import SecondaryVariantRow from "./variation/secondary-variant-row.vue"

@Component({
    name: "secondary-def-row",
    components: {SecondaryVariantRow},
})
export default class SecondaryDefRow extends Vue {
    @Prop({
        required: true
    })
    public readonly secondaryDef: ISecondaryDef;

    @Prop({
        default: true
    })
    public readonly showRefObjName: boolean;

    @Prop({
        default: false
    })
    public readonly isLoading: boolean;

    public variationsExpanded: boolean = false;

    public get getDescription() {
        return (this.showRefObjName ? this.secondaryDef.refObj.objName + " - "  : "" ) + this.secondaryDef.prod.name;
    }

    public get getMaxAllowed() {
        if (this.secondaryDef.maxAllowed === 0) {
            return "";
        }
        return this.secondaryDef.maxAllowed + (this.secondaryDef.perAcc.length > 0 ? "/" + this.secondaryDef.perAcc : "");
    }

    public get getQty() {
        return this.secondaryDef.prod.stockQty + " / " + this.secondaryDef.prod.soldQty;
    }

    public get getHasAttributes() {
        return this.secondaryDef.prod.attributes && (this.secondaryDef.prod.attributes.length > 0);
    }

    public get getHasVariations() {
        return this.secondaryDef.prod.variations && (this.secondaryDef.prod.variations.length > 0);
    }

    public get getAttributesDisplay(): string {
        return this.secondaryDef.prod.attributes.reduce( (accum, att) => {
            accum.push(att.name + ": " + att.values);
            return accum;
        }, [] as string[]).join(", ");
    }

    public onCancel() {
        this.$emit("onCancel");
    }

    public onDeleted() {
        this.$emit("onDeleted", this.secondaryDef);
    }

    public onEdited() {
        this.$emit("onEdited", R.clone(this.secondaryDef));
    }
}
</script>
