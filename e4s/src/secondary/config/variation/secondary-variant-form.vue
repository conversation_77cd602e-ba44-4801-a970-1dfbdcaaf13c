<template>
    <div>

        <div v-if="showSection === sections.FORM">
            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="col s12 m12 l12">
                    <div class="e4s-form-header">
                        Variant: <span v-text="secondaryVariantInternal.name"></span>
                    </div>
                </div>
            </div>

            <div class="e4s-section-padding-separator"></div>

            <div class="row">

                <div class="input-field col s4 m4 l4">

                    <label class="active" :for="PREFIX + '-sequence'">
                        Sequence
                        <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                        <FieldValidationLabel :validation-controller="validationController" prop-path="prod.sequence"/>
                    </label>

                    <input
                        :id="PREFIX + '-sequence'"
                        :name="PREFIX + '-sequence'"
                        type="number"
                        v-model.number="secondaryVariantInternal.menuOrder"
                        placeholder=""/>
                </div>

                <div class="input-field col s4 m4 l4">

                    <label class="active" :for="PREFIX + 'initial-quantity'">
                        Initial Quantity (0 = unlimited)
                        <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                        <FieldValidationLabel :validation-controller="validationController" prop-path="prod.stockQty"/>
                    </label>

                    <input
                        :id="PREFIX + 'initial-quantity'"
                        :name="PREFIX + 'initial-quantity'"
                        type="number"
                        v-model.number="secondaryVariantInternal.stockQty"
                        placeholder=""/>
                </div>

                <div class="input-field col s4 m4 l4">

                    <label class="active" :for="PREFIX + 'max-purchase-count'">
                        Price <a href="#" v-on:click.prevent="showPrice">Edit</a>
                        <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                        <FieldValidationLabel :validation-controller="validationController" prop-path="price"/>
                    </label>

                    <input
                        :id="PREFIX + 'price-price'"
                        :name="PREFIX + 'price-price'"
                        type="number"
                        v-model.number="secondaryVariantInternal.price.price"
                        placeholder=""/>
                </div>
            </div>

            <div class="e4s-section-padding-separator"></div>

<!--            <div>-->
<!--                <div class="row">-->
<!--                    <div class="col s12 m12 l12">-->
<!--                        <div class="e4s-form-header">-->
<!--                            Price-->
<!--                            <a href="#" v-on:click.prevent="showPrice">Edit</a>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->

<!--            <div class="e4s-section-padding-separator"></div>-->


            <div v-if="secondaryVariantInternal.soldQty === 0" v-for="(secondaryAttribute, index) in secondaryAttributes">
                <div class="row">
                    <div class="col s12 m6 l6">
                        <label class="active" :for="PREFIX + '-index'">
                            <span v-text="secondaryAttribute.name"></span>
                        </label>

                        <select :for="PREFIX + '-attr-index'" class="browser-default"
                                v-model="secondaryAttributeValuesKey[secondaryAttribute.name]">
                            <option value="">Any Selection</option>
                            <option v-for="option in secondaryService.getAttributesValues(secondaryAttribute)" v-bind:value="option">
                                {{ option }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="e4s-section-padding-separator"></div>

            <div class="row">
                <div class="input-field col s12 m6 l3">

                    <label class="active" :for="PREFIX + 'initial-image'">
                        Image
                        <FieldHelp help-key="builderCompetition.options.live"></FieldHelp>
                        <FieldValidationLabel :validation-controller="validationController" prop-path="prod.image"/>
                        <a :href="getOpenImageLink" target="_blank">
                            <span>Open</span>
                        </a>
                    </label>

                    <file-upload v-on:onUpload="onImageUpload"
                                 class="e4s-force-inline-block">
                    </file-upload>

                    <input
                        :id="PREFIX + 'initial-image'"
                        :name="PREFIX + 'initial-image'"
                        type="number"
                        v-model.number="secondaryVariantInternal.image"
                        placeholder=""/>
                </div>
            </div>

            <div class="row">
                <div class="input-field col s12 m12 l12">

                    <label class="active" :for="PREFIX + 'description'">
                        Description (Optional)
                        <FieldValidationLabel :validation-controller="validationController" prop-path="prod.description"/>
                    </label>

                    <input
                        :id="PREFIX + 'description'"
                        :name="PREFIX + 'description'"
                        type="text"
                        v-model="secondaryVariantInternal.description"
                        placeholder=""/>
                </div>

<!--                <div class="input-field col s12 m6 l3">-->
<!--                    <label class="active"-->
<!--                           for="competition-active">-->
<!--                        Download-->
<!--                        <FieldHelp help-key="prod.download"></FieldHelp>-->
<!--                    </label>-->
<!--                    <p>-->
<!--                        <label>-->
<!--                            <input id="competition-active"-->
<!--                                   class="e4s-checkbox"-->
<!--                                   type="checkbox"-->
<!--                                   v-model="secondaryVariantInternal.download" />-->
<!--                            <span class="e4s-bold">-->
<!--                                Active-->
<!--                            </span>-->
<!--                        </label>-->
<!--                    </p>-->
<!--                </div>-->
            </div>


            <div class="row">
                <div class="col s12 m12 l12">

                    <div class="right">
                        <LoadingSpinner v-if="isLoading"></LoadingSpinner>
                        <button class="btn waves-effect waves red"
                                :disabled="isLoading"
                                v-on:click.stop="cancel">
                            <span>Cancel</span>
                        </button>

                        <button class="btn waves-effect waves green"
                                :disabled="isLoading"
                                v-on:click.stop="submit">
                            <span>Save</span>
                        </button>
                    </div>

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col s12 m12 l12">
                {{secondaryVariantInternal}}
            </div>
        </div>
        <div class="row">
            <div class="col s12 m12 l12">
                secondaryAttributeValuesKey{{secondaryAttributeValuesKey}}
            </div>
        </div>



        <E4sModal v-if="showSection === sections.PRICE" :css-class="'e4s-modal-container--full-size'">
            <div slot="header"></div>
            <div slot="body">

                <StandardForm title="Price">
                    <div slot="form-content">
                        <div class="e4s-modal-container--full-size-form-content">
                            <PriceForm id="qwerty"
                                       :price-prop="secondaryPriceEdit"
                                       :is-admin="isAdmin"
                                       :suppress-save="true"
                                       :config-options="configApp.options"
                                       :is-national="isNational"
                                       v-on:onCancel="onPriceCancel"
                                       v-on:onSubmit="onPriceSubmit">
                            </PriceForm>
                        </div>
                    </div>
                </StandardForm>

            </div>
            <div slot="footer">
            </div>
        </E4sModal>

    </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {ISecondaryAttributeValue, ISecondaryAttributes, ISecondaryVariant, ISecondaryPrice} from "../../secondary-models"

import { SecondaryService} from "../../secondary-service";
import FieldHelp from "../../../common/ui/field/field-help/field-help.vue"
import FieldValidationLabel from "../../../validation/validation-field-lable.vue"
import {ValidationController} from "../../../validation/validation-controller"
import {IPrice} from "../../../price/price-models"
import {PriceService} from "../../../price/price-service"
import E4sModal from "../../../common/ui/e4s-modal.vue"
import StandardForm from "../../../common/ui/standard-form/standard-form.vue"
import PriceForm from "../../../price/price-form.vue"
import {mapGetters, mapState} from "vuex"
import {CONFIG_STORE_CONST, IConfigStoreState} from "../../../config/config-store"
import {IConfigApp} from "../../../config/config-app-models"
import {CommonService} from "../../../common/common-service"
import { SecondaryVariationService } from "./secondary-variation-service";
import FileUpload from "../../../common/ui/file-upload.vue"

const secondaryService: SecondaryService = new SecondaryService();

@Component({
    name: "secondary-variant-form",
    components: {
        FileUpload,
        PriceForm,
        StandardForm,
        E4sModal,
        FieldValidationLabel,
        FieldHelp
    },
    computed: {
        ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
            configApp: (state: IConfigStoreState) => state.configApp
        }),
        ...mapGetters(
            {
                isAdmin: CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME + "/" + CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN
            }
        )
    }
})
export default class SecondaryVariantForm extends Vue {
    public configApp: IConfigApp;

    @Prop({
        default: ""
    })
    public readonly name: string;

    @Prop({
        default: () => {
            return [];
        }
    })
    public readonly secondaryAttributes: ISecondaryAttributes[];

    @Prop({
        default: () => {
            return secondaryService.factorySecondaryVariant();
        }
    })
    public readonly secondaryVariant: ISecondaryVariant;

    @Prop({
        default: false
    })
    public readonly isLoading: boolean;

    @Prop({
        default: false
    })
    public readonly isNational: boolean;

    @Watch("secondaryVariant")
    public onSecondaryVariantChanged(newValue: ISecondaryVariant) {
        this.secondaryVariantInternal = R.clone(newValue);
        this.mapAttributes();
    }

    public commonService: CommonService = new CommonService();
    public secondaryService: SecondaryService = secondaryService;
    public secondaryVariationService: SecondaryVariationService = new SecondaryVariationService();
    public secondaryVariantInternal: ISecondaryVariant = this.secondaryService.factorySecondaryVariant();

    public PREFIX = Math.random().toString(36).substring(2);
    public validationController: ValidationController = new ValidationController();

    public priceService = new PriceService();
    public secondaryPriceEdit: ISecondaryPrice = this.secondaryService.factorySecondaryPrice()
    public sections = {
        FORM: "FORM",
        PRICE: "PRICE"
    };
    public showSection: string = this.sections.FORM;

    public secondaryAttributeValuesKey: Record<string, string> = {};
    public secondaryAttributeValues: ISecondaryAttributeValue[] =[];

    public created() {
        this.secondaryVariantInternal = R.clone(this.secondaryVariant);
        this.mapAttributes();
    }

    public showPrice() {
        const price = this.secondaryVariantInternal.price;
        this.secondaryPriceEdit = {
            ...this.priceService.factoryGetPrice(),
            price: price.price,
            salePrice: price.salePrice,
            saleEndDate: price.saleEndDate
        };
        this.showSection = this.sections.PRICE;
    }

    public onPriceCancel() {
        this.showSection = this.sections.FORM;
    }

    public onPriceSubmit(price: IPrice) {
        const secondaryVariant = R.clone(this.secondaryVariantInternal);
        secondaryVariant.price = {
            ...this.secondaryService.factorySecondaryPrice(),
            price: price.price,
            salePrice: price.salePrice,
            saleEndDate: price.saleEndDate
        }
        this.secondaryVariantInternal = secondaryVariant;
        this.showSection = this.sections.FORM;
    }

    public onImageUpload(imagePath: string) {
        this.secondaryVariantInternal.image = imagePath;
        if (this.secondaryVariantInternal.id > 0) {
            this.submit();
        }
    }

    public getOpenImageLink() {
        return this.secondaryVariantInternal.image;
    }

    public cancel() {
        this.$emit("onCancel");
    }

    public mapAttributes() {
        this.secondaryAttributeValuesKey = this.secondaryVariationService.mapAttributes(this.secondaryVariant, this.secondaryAttributes);
    }

    public submit() {
        // this.mapAttributes(false);
        const attrs = Object.keys(this.secondaryAttributeValuesKey).map((key) => {
            return {
                name: key,
                value: this.secondaryAttributeValuesKey[key]
            }
        })
        const secondaryVariant = R.clone(this.secondaryVariantInternal);
        secondaryVariant.attributeValues = attrs;
        this.$emit("onSubmit", secondaryVariant);
    }

}
</script>
