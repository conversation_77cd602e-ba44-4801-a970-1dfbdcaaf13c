<template>
  <div>
    <div class="row">
      <div class="col s12 m12 l12">
        <span v-text="getAthleteMessage"></span>
      </div>
    </div>

    <!--        <div class="row">-->
    <!--            <div class="col s12 m12 l12">-->
    <!--                <div v-text="$mq"></div>-->
    <!--                Is: {{$mq === VUE_MQ_SIZES.MOBILE.name}}-->
    <!--                <br>-->
    <!--                Not: {{$mq !== VUE_MQ_SIZES.MOBILE.name}}-->
    <!--                <br>-->
    <!--                <span v-if="$mq === VUE_MQ_SIZES.MOBILE.name">mobile!</span>-->
    <!--                <span v-if="$mq !== VUE_MQ_SIZES.MOBILE.name">not mobile!</span>-->
    <!--                <br>-->
    <!--                {{secondaryDef.id}} getHasVariations{{getHasVariations}}-->
    <!--            </div>-->
    <!--        </div>-->

    <div class="e4s-flex-column" v-if="!getHasVariations">
      <div
        class="
          e4s-flex-row e4s-justify-flex-row-vert-center
          e4s-gap--standard
          e4s-container--row-mobile-col
        "
      >
        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-align-self-flex-start e4s-justify-flex-row-vert-center
          "
        >
          <AttachmentThumbnail
            :image-link="secondaryDef.prod.image"
          ></AttachmentThumbnail>

          <span v-text="getDescription"></span>
        </div>

        <div
          class="
            e4s-flex-row
            e4s-gap--standard
            e4s-justify-flex-row-vert-center
            e4s-flex-row--end
          "
        >
          <span v-text="getPrice"></span>
          <span>X</span>
          <select
            class="
              browser-default
              e4s-input-field e4s-input-field--primary
              e4s-input--digit-number-small
            "
            :id="PREFIX + 'qty-required'"
            v-model.number="qtyRequired"
            v-if="!getHasAttributes && !getHasMaxAllowedBeenReached"
          >
            <option
              v-for="qty in getQtyAllowedOptions"
              :key="qty + ''"
              :value="qty"
            >
              {{ qty }}
            </option>
          </select>

          <slot name="select-button">
            <ButtonGenericV2
              class="e4s-button--60"
              text="Add"
              v-on:click="onSelectedParent"
              v-if="!getHasAttributes && !getHasMaxAllowedBeenReached"
            />
          </slot>
        </div>
      </div>

      <div class="e4s-flex-row e4s-flex-row--end">
        <ValidationFieldLable
          :validation-controller="validationController"
          prop-path="qtyRequired"
        />
      </div>
    </div>

    <!--    <div v-if="!getHasVariations">-->
    <!--      <div class="row">-->
    <!--        <div class="col s12 m12 l12">-->
    <!--          <div>-->
    <!--            <div class="col s2 m2 l2">-->
    <!--              <AttachmentThumbnail-->
    <!--                :image-link="secondaryDef.prod.image"-->
    <!--              ></AttachmentThumbnail>-->
    <!--            </div>-->

    <!--            <div class="col s10 m10 l10">-->
    <!--              <div class="row">-->
    <!--                <div class="col s12 m12 l12">-->
    <!--                  <span v-text="getDescription"></span>-->

    <!--                  <div class="right e4s-bold">-->
    <!--                    <span v-text="getPrice"></span>-->
    <!--                  </div>-->
    <!--                </div>-->

    <!--                <div class="col s12 m12 l12">-->
    <!--                  <div-->
    <!--                    class="right"-->
    <!--                    v-if="!getHasAttributes && !getHasMaxAllowedBeenReached"-->
    <!--                  >-->
    <!--                    &lt;!&ndash;                                        &ndash;&gt;-->
    <!--                    <select-->
    <!--                      class="e4s-select e4s-select-number"-->
    <!--                      :id="PREFIX + 'qty-required'"-->
    <!--                      v-model.number="qtyRequired"-->
    <!--                    >-->
    <!--                      <option-->
    <!--                        v-for="qty in getQtyAllowedOptions"-->
    <!--                        :key="qty + ''"-->
    <!--                        :value="qty"-->
    <!--                      >-->
    <!--                        {{ qty }}-->
    <!--                      </option>-->
    <!--                    </select>-->

    <!--                    <slot name="select-button">-->
    <!--                      <button-->
    <!--                        class="btn waves-effect waves green"-->
    <!--                        :disabled="isLoading"-->
    <!--                        v-on:click.stop="onSelectedParent"-->
    <!--                      >-->
    <!--                        <span>Add</span>-->
    <!--                      </button>-->
    <!--                    </slot>-->

    <!--                    <div>-->
    <!--                      <ValidationFieldLable-->
    <!--                        :validation-controller="validationController"-->
    <!--                        prop-path="qtyRequired"-->
    <!--                      />-->
    <!--                    </div>-->
    <!--                  </div>-->
    <!--                </div>-->
    <!--              </div>-->
    <!--            </div>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->

    <div class="row" v-if="getHasVariations">
      <div
        v-for="(variation, index) in secondaryDef.prod.variations"
        class="input-field col s12 m12 l12"
      >
        <SecondaryCustVariantRow
          :secondary-variant="variation"
          :secondary-def="secondaryDef"
          :max-allowed="getQtyAllowed"
          :is-loading="isLoading"
          :has-max-allowed-been-reached="getHasMaxAllowedBeenReached"
          v-on:onSelectedVariant="onSelectedVariant"
        >
          <div slot="delete-button"></div>
        </SecondaryCustVariantRow>
      </div>
    </div>

    <!--      <div class="row">-->
    <!--        <div class="input-field col s12 m12 l12">-->
    <!--            getQtyCart{{getQtyCart}}<br>-->
    <!--            getQtyAthlete{{ getQtyCartAthlete }}<br>-->
    <!--            getQtyPurchasedAthlete{{getQtyPurchasedAthlete}}<br>-->
    <!--            getQtyPurchased{{getQtyPurchased}}<br>-->
    <!--            getWooCartAthleteKey{{getWooCartAthleteKey}}-->
    <!--        </div>-->
    <!--      </div>-->

    <!--    <div class="row" v-if="getHasMaxAllowedBeenReached">-->
    <!--      <div class="col s12 m12 l12">Max purchase has been reached.</div>-->
    <!--    </div>-->

    <InfoSectionV2 info-type="warn" v-if="getHasMaxAllowedBeenReached">
      <div class="e4s-flex-row">Max purchase has been reached.</div>
    </InfoSectionV2>

    <!--    <div class="row" v-if="getPurchasedMessage.length > 0">-->
    <!--      <div class="col s12 m12 l12">-->
    <!--        <span v-text="getPurchasedMessage"></span>-->
    <!--      </div>-->
    <!--    </div>-->

    <InfoSectionV2 info-type="warn" v-if="getPurchasedMessage.length > 0">
      <div v-text="getPurchasedMessage"></div>
    </InfoSectionV2>

    <div class="row" v-show="false">
      <div class="col s6 m6 l6">perAcc: {{ secondaryDef.perAcc }}</div>
      <div class="col s6 m6 l6">getHasVariations: {{ getHasVariations }}</div>
      <div class="col s6 m6 l6">
        getQtyPurchasedAthlete: {{ getQtyPurchasedAthlete }}
      </div>
      <div class="col s6 m6 l6">getQtyPurchased: {{ getQtyPurchased }}</div>
      <div class="col s6 m6 l6">getQtyCartAthlete: {{ getQtyCartAthlete }}</div>
      <div class="col s6 m6 l6">getQtyCart: {{ getQtyCart }}</div>
      <div class="col s6 m6 l6">
        getHasMaxAllowedBeenReached: {{ getHasMaxAllowedBeenReached }}
      </div>
      <div class="col s6 m6 l6">getQtyAllowed: {{ getQtyAllowed }}</div>
    </div>

    <!--        <div class="row">-->
    <!--            <div class="col s12 m12 l12">-->
    <!--                secondaryPurchaseReadAthlete: {{secondaryPurchaseReadAthlete}}-->
    <!--            </div>-->
    <!--        </div>-->
  </div>
</template>

<script lang="ts">
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  ISecondaryAttributeValue,
  ISecondaryDef,
  IWooCommerceLineItem,
} from "../secondary-models";
import * as R from "ramda";
import SecondaryCustVariantRow from "./secondary-cust-variant-row.vue";
import SecondaryCustAttrPicker from "./secondary-cust-attr-picker.vue";
import {
  ISecondaryPurchaseParentEmit,
  ISecondaryPurchaseVariantEmit,
  ISecondaryPurchaseRead,
} from "./secondary-cust-models";
import { ValidationController } from "../../validation/validation-controller";
import ValidationFieldLable from "../../validation/validation-field-lable.vue";
import { IValidationProp } from "../../validation/validation-models";
import { ValidationService } from "../../validation/validation-service";
import { IAthlete } from "../../athlete/athlete-models";
import { IAthleteCompSchedRuleEvent } from "../../athletecompsched/athletecompsched-models";
import { mapState } from "vuex";
import {
  ATH_COMP_SCHED_STORE_CONST,
  IAthCompSchedStoreState,
} from "../../athleteCompSched/store/athleteCompSched-store";
import { ENTRY_STORE_CONST, IEntryStoreState } from "../../entry/entry-store";
import { SecondaryService } from "../secondary-service";
import AttachmentThumbnail from "../../common/ui/attachments/attachment-thumbnail.vue";
import { VUE_MQ_SIZES } from "../..";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../config/config-store";
import { IConfigApp } from "../../config/config-app-models";
import InfoSectionV2 from "../../common/ui/layoutV2/info-section-v2.vue";
import ButtonGenericV2 from "../../common/ui/layoutV2/buttons/button-generic-v2.vue";

@Component({
  name: "secondary-cust-def-row",
  components: {
    ButtonGenericV2,
    InfoSectionV2,
    AttachmentThumbnail,
    ValidationFieldLable,
    SecondaryCustAttrPicker,
    SecondaryCustVariantRow,
  },
  computed: {
    ...mapState(ATH_COMP_SCHED_STORE_CONST.ATH_COMP_SCHED_CONST_MODULE_NAME, {
      eventsSelected: (state: IAthCompSchedStoreState) => state.eventsSelected,
      eventsAvailableToAthlete: (state: IAthCompSchedStoreState) =>
        state.eventsProcessed,
    }),
    ...mapState(ENTRY_STORE_CONST.ENTRY_STORE_CONST_MODULE_NAME, {
      selectedAthlete: (state: IEntryStoreState) =>
        state.entryForm.selectedAthlete,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: IConfigStoreState) => state.configApp,
    }),
  },
})
export default class SecondaryCustDefRow extends Vue {
  public readonly selectedAthlete: IAthlete;
  public readonly eventsSelected: IAthleteCompSchedRuleEvent[];
  public readonly eventsAvailableToAthlete: IAthleteCompSchedRuleEvent[];
  public configApp: IConfigApp;

  @Prop({
    required: true,
  })
  public readonly secondaryDef: ISecondaryDef;

  @Prop({
    default: true,
  })
  public readonly showRefObjName: boolean;

  @Prop({
    default: false,
  })
  public readonly isLoading: boolean;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly secondaryPurchaseRead: ISecondaryPurchaseRead[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly secondaryPurchaseReadUser: ISecondaryPurchaseRead[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly secondaryPurchaseReadAthlete: ISecondaryPurchaseRead[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly athleteEnteredEvents: IAthleteCompSchedRuleEvent[];

  @Prop({
    default: false,
  })
  public readonly hasAthleteEnteredAnyEvents: boolean;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly secondaryCartForThisItem!: IWooCommerceLineItem[];

  public validationController: ValidationController =
    new ValidationController();
  public variationsExpanded: boolean = false;
  public PREFIX = Math.random().toString(36).substring(2);
  public secondaryAttributeValuesSelected: Record<string, string> = {};
  public qtyRequired = 0;
  public qtyRequiredOptions: string[] = [];
  public secondaryService: SecondaryService = new SecondaryService();
  public $mq: any;
  public VUE_MQ_SIZES = VUE_MQ_SIZES;

  public created() {
    if (
      this.secondaryPurchaseReadUser &&
      this.secondaryPurchaseReadUser.length > 0
    ) {
      const secondaryPurchaseReadUser =
        this.secondaryService.getSecondaryPurchaseReadForAthleteId(
          this.selectedAthlete.id,
          this.secondaryPurchaseReadUser
        );
      console.log("secondaryPurchaseReadUser", secondaryPurchaseReadUser);
    }
  }

  public get getDescription() {
    // return (this.showRefObjName ? this.secondaryDef.refObj.objName + " - "  : "" ) +
    //     this.secondaryDef.prod.name + " (" + this.secondaryDef.prod.id  + ")";
    return (
      (this.showRefObjName ? this.secondaryDef.refObj.objName + " - " : "") +
      this.secondaryDef.prod.name
    );
  }

  public get getMaxAllowedText() {
    if (this.secondaryDef.maxAllowed === 0) {
      return "";
    }
    return (
      this.secondaryDef.maxAllowed +
      (this.secondaryDef.perAcc.length > 0
        ? "/" + this.secondaryDef.perAcc
        : "")
    );
  }

  @Watch("secondaryPurchaseReadAthlete")
  public onSecondaryPurchaseReadAthleteChanged(
    newValue: ISecondaryPurchaseRead[],
    oldValue: ISecondaryPurchaseRead[]
  ) {
    if (newValue && newValue.length > 0) {
      const secondaryPurchaseReadUser =
        this.secondaryService.getSecondaryPurchaseReadForAthleteId(
          this.selectedAthlete.id,
          this.secondaryPurchaseReadUser
        );
      console.log("secondaryPurchaseReadUser", secondaryPurchaseReadUser);
    }
  }

  public get getQtyPurchasedAthlete(): number {
    if (this.secondaryDef.perAcc === "A") {
      const secondaryPurchaseReadUser =
        this.secondaryService.getSecondaryPurchaseReadForAthleteId(
          this.selectedAthlete.id,
          this.secondaryPurchaseReadAthlete
        );
      const total = secondaryPurchaseReadUser.reduce((accum, purchase) => {
        accum = accum + purchase.qtyPurchased;
        return accum;
      }, 0);
      return total;
    }
    return 0;
  }

  public get getQtyPurchased(): number {
    let secondaryPurchaseReadUser = this.secondaryPurchaseReadUser;
    if (this.secondaryDef.perAcc === "A") {
      secondaryPurchaseReadUser =
        this.secondaryService.getSecondaryPurchaseReadForAthleteId(
          this.selectedAthlete.id,
          secondaryPurchaseReadUser
        );
    }
    return secondaryPurchaseReadUser.reduce((accum, purchase) => {
      accum = accum + purchase.qtyPurchased;
      return accum;
    }, 0);
  }

  public get getQtyCartAthlete(): number {
    if (this.secondaryDef.perAcc === "A") {
      const prodAthleteKey =
        this.secondaryDef.prod.id + "~" + this.selectedAthlete.id;
      if (this.getWooCartAthleteKey[prodAthleteKey]) {
        return this.getWooCartAthleteKey[prodAthleteKey].reduce(
          (accum, wooCommerceLineItem: IWooCommerceLineItem) => {
            return accum + wooCommerceLineItem.qty;
          },
          0
        );
      }
      return 0;
    } else {
      return 0;
    }
  }

  public get getQtyCart(): number {
    return this.secondaryCartForThisItem.reduce(
      (accum, cart: IWooCommerceLineItem) => {
        accum = accum + cart.qty;
        return accum;
      },
      0
    );
  }

  public get getHasMaxAllowedBeenReached(): boolean {
    if (this.secondaryDef.maxAllowed > 0) {
      if (this.secondaryDef.perAcc === "A") {
        return (
          this.getQtyPurchasedAthlete + this.getQtyCartAthlete >=
          this.secondaryDef.maxAllowed
        );
      }
      return (
        this.getQtyPurchased + this.getQtyCart >= this.secondaryDef.maxAllowed
      );
    }
    return false;
  }

  public get getQtyText() {
    return this.secondaryDef.prod.stockQty;
  }

  public get getHasAttributes(): boolean {
    if (!this.secondaryDef.prod.attributes) {
      return false;
    }
    return this.secondaryDef.prod.attributes.length > 0;
  }

  public get getHasVariations(): boolean {
    if (!this.secondaryDef.prod.variations) {
      return false;
    }
    return this.secondaryDef.prod.variations.length > 0;
  }

  public get getAttributesDisplay(): string {
    return this.secondaryDef.prod.attributes
      .reduce((accum, att) => {
        accum.push(att.name + ": " + att.values);
        return accum;
      }, [] as string[])
      .join(", ");
  }

  public get getAthleteMessage(): string {
    if (this.secondaryDef.perAcc !== "A") {
      return "";
    }
    if (!(this.selectedAthlete.id && this.selectedAthlete.id > 0)) {
      return "This item requires athlete selection";
    }
    if (this.eventsAvailableToAthlete.length === 0) {
      return "This item requires athlete an athlete to have available events";
    }
    if (!this.hasAthleteEnteredAnyEvents) {
      return "This item requires athlete an athlete to have entered an event";
    }
    return "";
  }

  public get getIsAthleteSelectionRequired() {
    if (this.secondaryDef.perAcc !== "A") {
      return false;
    }
    return !(
      this.selectedAthlete.id &&
      this.selectedAthlete.id > 0 &&
      this.eventsAvailableToAthlete.length > 0
    );
  }

  public get getWooCartAthleteKey(): Record<string, IWooCommerceLineItem[]> {
    const wooByProdAndAthleteId: Record<string, IWooCommerceLineItem[]> =
      this.secondaryCartForThisItem.reduce(
        (accum, wooCommerceLineItem: IWooCommerceLineItem) => {
          const athleteId = this.secondaryService.getAthleteIdFromVariations(
            wooCommerceLineItem.variation
          );

          const key = wooCommerceLineItem.productId + "~" + athleteId;
          if (!accum[key]) {
            accum[key] = [];
          }
          accum[key].push(wooCommerceLineItem);
          return accum;
        },
        {} as Record<string, IWooCommerceLineItem[]>
      );

    return wooByProdAndAthleteId;
  }

  public onAttributeValueSelection(
    secondaryAttributeValuesSelected: Record<string, string>
  ) {
    this.secondaryAttributeValuesSelected = secondaryAttributeValuesSelected;
  }

  public validate() {
    this.validationController.reset();

    const validationService: ValidationService = new ValidationService();
    let validationState: Record<string, IValidationProp> = {};

    if (this.qtyRequired <= 0) {
      validationState = validationService.addMessage(
        "qtyRequired",
        "Value more than zero.",
        validationState
      );
    }

    if (this.getHasAttributes) {
      // const attributeValuesSelected: boolean = Object.keys(this.secondaryAttributeValuesSelected).reduce( (accum, propName) => {
      //     if (this.secondaryAttributeValuesSelected[propName].length === 0) {
      //         accum = false;
      //     }
      //     return accum;
      // }, true as boolean)
      const attributeValuesSelected: number = Object.keys(
        this.secondaryAttributeValuesSelected
      ).reduce((accum, propName) => {
        if (this.secondaryAttributeValuesSelected[propName].length > 0) {
          accum++;
        }
        return accum;
      }, 0);

      if (
        this.secondaryDef.prod.attributes.length !== attributeValuesSelected
      ) {
        validationState = validationService.addMessage(
          "attributes",
          "Please select all attributes",
          validationState
        );
      }
    }

    if (this.getHasVariations) {
      // has user
    }

    this.validationController.setErrors(validationState);
  }

  public onSelectedParent() {
    const secondaryAttributeValuesSelected: ISecondaryAttributeValue[] =
      Object.keys(this.secondaryAttributeValuesSelected).map((key) => {
        return {
          name: key,
          value: this.secondaryAttributeValuesSelected[key],
        };
      });

    const secondaryPurchaseParent: ISecondaryPurchaseParentEmit = {
      secondaryDef: this.secondaryDef,
      prod: this.secondaryDef.prod,
      qtyRequired: this.qtyRequired,
      attributeValues: secondaryAttributeValuesSelected,
    };

    this.validate();
    if (!this.validationController.isValid) {
      return;
    }

    this.$emit("onSelectedParent", R.clone(secondaryPurchaseParent));
  }

  public get getCanShowAdd() {
    return !this.getHasMaxAllowedBeenReached;
  }

  public get getPrice() {
    return this.configApp.currency + this.secondaryDef.prod.price.price;
  }

  public get getQtyAllowed() {
    if (this.secondaryDef.maxAllowed === 0) {
      return 100;
    }

    let maxQty = 100;
    if (this.secondaryDef.perAcc === "A") {
      maxQty =
        this.secondaryDef.maxAllowed -
        (this.getQtyPurchasedAthlete + this.getQtyCartAthlete);
    } else {
      maxQty =
        this.secondaryDef.maxAllowed - (this.getQtyPurchased + this.getQtyCart);
    }
    return maxQty < 0 ? 0 : maxQty;
  }

  public get getQtyAllowedOptions(): string[] {
    const options = [];

    //  because we are starting at zero, we need to go one more than the max allowed
    const loopUntil = this.getQtyAllowed + 1;

    for (let i = 0; i < loopUntil; i++) {
      options.push(i + "");
    }
    return options;
  }

  public get getPurchasedMessage() {
    const messages: string[] = [];

    if (this.secondaryDef.perAcc === "A") {
      this.getQtyPurchasedAthlete > 0
        ? messages.push(this.getQtyPurchasedAthlete + " already purchased.")
        : null;
      this.getQtyCartAthlete > 0
        ? messages.push(this.getQtyCartAthlete + " in athlete cart")
        : null;
    } else {
      this.getQtyPurchased > 0
        ? messages.push(this.getQtyPurchased + " already purchased.")
        : null;
      this.getQtyCart > 0 ? messages.push(this.getQtyCart + " in cart") : null;
    }
    return messages.join(" / ");
  }

  public onSelectedVariant(
    secondaryPurchaseVariant: ISecondaryPurchaseVariantEmit
  ) {
    const secondaryPurchaseParent: ISecondaryPurchaseParentEmit = {
      secondaryDef: this.secondaryDef,
      prod: secondaryPurchaseVariant.variant,
      qtyRequired: secondaryPurchaseVariant.qtyRequired,
      attributeValues: secondaryPurchaseVariant.attributeValues,
    };
    this.$emit("onSelectedVariant", R.clone(secondaryPurchaseParent));
  }
}
</script>
