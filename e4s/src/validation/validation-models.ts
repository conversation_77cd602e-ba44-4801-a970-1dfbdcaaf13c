import { IObjectKeyType } from "../common/common-models";

export interface IValidationMessage {
  title?: string;
  subTitle?: string;
  propPath: string;
  message: string;
}

export interface IValidationProp {
  title?: string;
  subTitle?: string;
  propPath: string;
  messages: string[];
}

export interface IValidationState {
  isValid: boolean;
  errors: IObjectKeyType<IValidationProp>;
}

// create a type that is a record of keys of T, with values of string[]
// TS2315: Type 'Record' is not generic.
export type ValidationMap<T> = Record<string | keyof T, string[]>;
// export type ValidationMap = Record<string, string[]>;

// export type ValidationMap2<T> = Record<keyof T, string[]>;
