
Schedule
select eg.id egId,       eg.eventNo,       eg.name eventName,       eg.startdate eventStartDate,       eg.typeNo,       eg.options egOptions,       ce.id ceId,       ce.ageGroupId,       ag.Name ageGroup,       ag.keyName ageGroupKey from Entry4_uk_EventGroups eg,        Entry4_uk_CompEvents ce,        Entry4_AgeGroups agwhere eg.compid = 617 and eg.id = ce.maxGroupand ce.ageGroupId = ag.id order by eg.eventNo;
or
select * from Entry4_uk_ScheduleView where compid = ???

Indiv Entries
select e.id             entryId,       e.compEventId    ceId,       e.athleteid      athleteId,       a.firstName,       a.surName,       ag.Name          ageGroupName,       b.bibno          athleteBibNo,       e.teambibno      teamBibNo,       c.Clubname       athleteClub,       e.checkedIn,       e.confirmed,       e.present,       e.pb,       s.heatNo,       s.lane<PERSON>o,       s.heatNo<PERSON>heckedIn,       s.laneNoCheckedIn from   Entry4_uk_Entries e join Entry4_uk_CompEvents ce on (e.compEventId = ce.id) left outer join Entry4_uk_Seeding s on (s.eventgroupid = ce.maxGroup and s.athleteid = e.athleteid),       Entry4_Athlete   a,       Entry4_Clubs     c,       Entry4_uk_Bibno  b,       Entry4_AgeGroups ag where e.athleteid = a.id  and e.clubid = c.id  and e.paid in ( 1,3 )  and b.compid = ce.CompID  and b.athleteid = a.id  and ag.id = e.ageGroupID  and ce.CompID = 617 order by ceid, heatno, laneno;
or
select * from Entry4_uk_IndivEntriesView where compid = ???
 
team entries
select e.id entryId,       e.ceid        ceId,       eg.id         egId,       eg.name       eventName,       ea.athleteid  athleteId,       a.firstName,       a.surName,       e.bibNo       teamBibNo,       e.name        teamName,       e.present,       s.heatno,       s.laneno,       s.heatnocheckedin,       s.lanenocheckedin from Entry4_uk_EventTeamEntries e left outer join Entry4_uk_EventTeamAthlete eaon e.id = ea.teamentryid left outer join Entry4_Athlete a on ea.athleteid = a.id join Entry4_uk_CompEvents ce on ( ce.id = e.ceid) join Entry4_uk_EventGroups eg on ( eg.id = ce.maxGroup) left outer join Entry4_uk_Seeding s on (s.eventgroupid = eg.id and s.athleteid = e.id) where e.ceid = ce.id  and e.paid in ( 1,3 )  and ce.CompID = 617 order by e.ceid; 
or 
or
Select * from Entry4_uk_TeamEntriesView where compid = ???

results.
select rh.id rhId,       eg.id egId,       rd.id rdId,       rh.heatno,       rd.lane,       rd.position,       rd.athleteid,       rd.athlete,       rd.club,       rd.bibno bibNo,       rd.score,       rd.qualify,       rd.scoreText,       rd.agegroup ageGroup,       rd.gender,       rd.wind,       rd.eaAward,       cr.resultKey,       cr.resultValue,       cr.options crOptions from Entry4_uk_EventResultsHeader rh join Entry4_uk_EventResults rd on ( rh.id = rd.resultheaderid) join     Entry4_uk_EventGroups eg on ( eg.id = rh.egid) left outer join Entry4_uk_CardResults cr on (cr.compid = eg.compid and cr.eventno = eg.eventno and cr.athleteid = rd.athleteid) where eg.compId = 617 order by eg.id, rh.heatno, rd.position
 OR
select * from Entry4_uk_ResultsView where compid = ???